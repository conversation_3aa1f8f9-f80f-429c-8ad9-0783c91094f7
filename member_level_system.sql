-- =====================================================
-- 会员等级系统数据表创建脚本
-- 符合FastAdmin规范设计
-- 创建时间：2025-01-27
-- =====================================================

-- 1. 会员等级配置表
DROP TABLE IF EXISTS `fa_memberlevel`;
CREATE TABLE `fa_memberlevel` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '等级名称',
  `level` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '等级序号',
  `consume_score` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '所需消费分',
  `icon` varchar(255) NOT NULL DEFAULT '' COMMENT '等级图标',
  `color` varchar(20) NOT NULL DEFAULT '#333333' COMMENT '等级颜色',
  `benefits` text COMMENT '权益配置JSON',
  `description` text COMMENT '等级描述',
  `status` enum('0','1') NOT NULL DEFAULT '1' COMMENT '状态:0=隐藏,1=显示',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `createtime` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `level` (`level`),
  KEY `status` (`status`),
  KEY `weigh` (`weigh`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员等级配置表';

-- 2. 用户会员等级记录表
DROP TABLE IF EXISTS `fa_usermember`;
CREATE TABLE `fa_usermember` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `level_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '当前等级ID',
  `level` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '当前等级序号',
  `consume_score` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '累计消费分',
  `total_recharge` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consume` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `upgrade_time` bigint(16) unsigned DEFAULT NULL COMMENT '最后升级时间',
  `expire_time` bigint(16) unsigned DEFAULT NULL COMMENT '等级过期时间',
  `status` enum('0','1') NOT NULL DEFAULT '1' COMMENT '状态:0=过期,1=正常',
  `createtime` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `level_id` (`level_id`),
  KEY `level` (`level`),
  KEY `consume_score` (`consume_score`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会员等级记录表';

-- 3. 会员权益使用记录表
DROP TABLE IF EXISTS `fa_benefitlog`;
CREATE TABLE `fa_benefitlog` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `level_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '等级ID',
  `benefit_type` varchar(50) NOT NULL DEFAULT '' COMMENT '权益类型',
  `benefit_name` varchar(100) NOT NULL DEFAULT '' COMMENT '权益名称',
  `used_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '已使用次数',
  `total_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '总可用次数',
  `order_id` int(10) unsigned DEFAULT NULL COMMENT '关联订单ID',
  `month` varchar(7) NOT NULL DEFAULT '' COMMENT '月份(YYYY-MM)',
  `expire_time` bigint(16) unsigned DEFAULT NULL COMMENT '过期时间',
  `status` enum('0','1','2') NOT NULL DEFAULT '1' COMMENT '状态:0=过期,1=正常,2=已用完',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `level_id` (`level_id`),
  KEY `benefit_type` (`benefit_type`),
  KEY `month` (`month`),
  KEY `status` (`status`),
  KEY `order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员权益使用记录表';

-- 4. 会员等级升级记录表
DROP TABLE IF EXISTS `fa_upgradelog`;
CREATE TABLE `fa_upgradelog` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `from_level` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '原等级',
  `to_level` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '新等级',
  `consume_score` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '升级时消费分',
  `trigger_type` varchar(50) NOT NULL DEFAULT '' COMMENT '触发类型:consume=消费,recharge=充值,manual=手动,system=系统',
  `trigger_id` int(10) unsigned DEFAULT NULL COMMENT '触发来源ID',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) unsigned DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `from_level` (`from_level`),
  KEY `to_level` (`to_level`),
  KEY `trigger_type` (`trigger_type`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员等级升级记录表';

-- 5. 插入默认会员等级配置
INSERT INTO `fa_memberlevel` (`id`, `name`, `level`, `consume_score`, `icon`, `color`, `benefits`, `description`, `status`, `weigh`, `createtime`, `updatetime`) VALUES
(1, 'VIP1', 1, 1000, '/assets/img/member/vip1.png', '#CD853F', '{\"free_shipping\":{\"count\":1,\"weight_limit\":5,\"type\":\"express\"},\"lottery\":{\"extra_count\":1},\"discount\":{\"rate\":100}}', 'VIP1会员享受免5KG单包快递费1次/月，加1次转盘抽奖次数/月', '1', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 'VIP2', 2, 3000, '/assets/img/member/vip2.png', '#C0C0C0', '{\"free_shipping\":{\"count\":2,\"weight_limit\":5,\"type\":\"all\"},\"lottery\":{\"extra_count\":2},\"discount\":{\"rate\":100}}', 'VIP2会员享受免5KG单包快递费或任意规格单包送货费2次/月，加2次转盘抽奖次数/月', '1', 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, 'VIP3', 3, 8000, '/assets/img/member/vip3.png', '#FFD700', '{\"free_shipping\":{\"count\":3,\"weight_limit\":5,\"type\":\"all\"},\"lottery\":{\"extra_count\":3},\"discount\":{\"rate\":100}}', 'VIP3会员享受免5KG单包快递费或任意规格单包送货费3次/月，加3次转盘抽奖次数/月', '1', 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, 'VIP4', 4, 15000, '/assets/img/member/vip4.png', '#E6E6FA', '{\"free_shipping\":{\"count\":3,\"weight_limit\":5,\"type\":\"all\"},\"lottery\":{\"extra_count\":3},\"discount\":{\"rate\":97}}', 'VIP4会员享受免5KG单包快递费或任意规格单包送货费3次/月+全单97折，加3次转盘抽奖次数/月', '1', 4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(5, 'VIP5', 5, 30000, '/assets/img/member/vip5.png', '#FF1493', '{\"free_shipping\":{\"count\":999,\"weight_limit\":5,\"type\":\"all\"},\"lottery\":{\"extra_count\":4},\"discount\":{\"rate\":97}}', 'VIP5会员享受免5KG单包快递费或任意规格单包送货费+全单97折，加4次转盘抽奖次数/月', '1', 5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 6. 为现有用户创建默认会员记录（等级0，消费分0）
INSERT INTO `fa_usermember` (`user_id`, `level_id`, `level`, `consume_score`, `total_recharge`, `total_consume`, `status`, `createtime`, `updatetime`)
SELECT
    id as user_id,
    0 as level_id,
    0 as level,
    0 as consume_score,
    0.00 as total_recharge,
    0.00 as total_consume,
    '1' as status,
    UNIX_TIMESTAMP() as createtime,
    UNIX_TIMESTAMP() as updatetime
FROM fa_user
WHERE NOT EXISTS (
    SELECT 1 FROM fa_usermember WHERE fa_usermember.user_id = fa_user.id
);

-- 7. 创建索引优化查询性能
ALTER TABLE `fa_memberlevel` ADD INDEX `idx_consume_score` (`consume_score`);
ALTER TABLE `fa_usermember` ADD INDEX `idx_user_level` (`user_id`, `level`);
ALTER TABLE `fa_benefitlog` ADD INDEX `idx_user_month_type` (`user_id`, `month`, `benefit_type`);
ALTER TABLE `fa_upgradelog` ADD INDEX `idx_user_time` (`user_id`, `createtime`);

-- =====================================================
-- 数据表创建完成
-- 
-- 功能说明：
-- 1. fa_memberlevel: 会员等级配置，支持后台管理
-- 2. fa_usermember: 用户会员状态记录
-- 3. fa_benefitlog: 权益使用记录，按月统计
-- 4. fa_upgradelog: 等级升级历史记录
-- 
-- 权益配置JSON格式说明：
-- {
--   "free_shipping": {
--     "count": 次数,
--     "weight_limit": 重量限制(KG),
--     "type": "express|delivery|all"
--   },
--   "lottery": {
--     "extra_count": 额外转盘次数
--   },
--   "discount": {
--     "rate": 折扣率(97表示97折)
--   }
-- }
-- =====================================================
