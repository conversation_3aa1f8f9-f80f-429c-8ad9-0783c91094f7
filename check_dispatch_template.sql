-- 检查配送模板数据
-- 查看所有配送模板
SELECT * FROM fa_shopro_dispatch;

-- 查看ID为20的配送模板
SELECT * FROM fa_shopro_dispatch WHERE id = 20;

-- 查看ID为20的配送模板的配送规则
SELECT * FROM fa_shopro_dispatch_express WHERE dispatch_id = 20;

-- 查看所有上门配送的配送规则（is_shang=1）
SELECT d.id, d.name, d.type, e.* 
FROM fa_shopro_dispatch d 
LEFT JOIN fa_shopro_dispatch_express e ON d.id = e.dispatch_id 
WHERE e.is_shang = 1;

-- 如果ID为20的配送模板不存在，创建一个示例模板
-- INSERT INTO fa_shopro_dispatch (id, name, type, status, createtime, updatetime) 
-- VALUES (20, '上门配送模板', 'express', 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 创建上门配送规则（覆盖全国）
-- INSERT INTO fa_shopro_dispatch_express (
--     dispatch_id, type, first_num, first_price, additional_num, additional_price,
--     free_shipping_num, free_shipping_weight, province_ids, city_ids, district_ids, is_shang, weigh
-- ) VALUES (
--     20, 'number', 1, 10.00, 1, 5.00,
--     0, 0.00, '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34', 
--     '', '', 1, 0
-- );
