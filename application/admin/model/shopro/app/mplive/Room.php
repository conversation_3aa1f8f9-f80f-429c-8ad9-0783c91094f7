<?php

namespace app\admin\model\shopro\app\mplive;

use app\admin\model\shopro\Common;

class Room extends Common
{

    protected $name = 'shopro_mplive_room';

    protected $append = [
        'status_text',
        'type_text',
    ];

    const ERR_CODE = [
        -1 => '系统错误',
        1 => '未创建直播间',
        1003 => '商品 id 不存在',
        47001 => '入参格式不符合规范',
        200002 => '入参错误',
        300001 => '禁止创建/更新商品 或 禁止编辑/更新房间',
        300002 => '名称长度不符合规则',
        300003 => '价格输入不合规（如：现价比原价大、传入价格非数字等）',
        300004 => '商品名称存在违规违法内容',
        300005 => '商品图片存在违规违法内容',
        300006 => ' 图片上传失败（如：mediaID过期）',
        300007 => '线上小程序版本不存在该链接',
        300008 => '添加商品失败',
        300009 => '商品审核撤回失败',
        300010 => '商品审核状态不对（如：商品审核中）',
        300011 => '操作非法（API不允许操作非 API 创建的商品）',
        300012 => '没有提审额度（每天500次提审额度）',
        300013 => '提审失败',
        300014 => '审核中，无法删除（非零代表失败）',
        300017 => '商品未提审',
        300018 => '商品图片尺寸过大',
        300021 => '商品添加成功，审核失败',
        300022 => '此房间号不存在',
        300023 => '房间状态 拦截（当前房间状态不允许此操作）',
        300024 => '商品不存在',
        300025 => '商品审核未通过',
        300026 => '房间商品数量已经满额',
        300027 => '导入商品失败',
        300028 => '房间名称违规',
        300029 => '主播昵称违规',
        300030 => '主播微信号不合法',
        300031 => '直播间封面图不合规',
        300032 => '直播间分享图违规',
        300033 => '添加商品超过直播间上限',
        300034 => '主播微信昵称长度不符合要求',
        300035 => '主播微信号不存在',
        300036 => '主播微信号未实名认证',
        300037 => '购物直播频道封面图不合规',
        300038 => '未在小程序管理后台配置客服',
        300039 => '主播副号微信号不合法',
        300040 => '名称含有非限定字符（含有特殊字符）',
        300041 => '创建者微信号不合法',
        300042 => '推流中禁止编辑房间',
        300043 => '每天只允许一场直播开启关注',
        300044 => '商品没有讲解视频',
        300045 => '讲解视频未生成',
        300046 => '讲解视频生成失败',
        300047 => '已有商品正在推送，请稍后再试',
        300048 => '拉取商品列表失败',
        300049 => '商品推送过程中不允许上下架',
        300050 => '排序商品列表为空',
        300051 => '解析 JSON 出错',
        300052 => '已下架的商品无法推送',
        300053 => '直播间未添加此商品',
        500001 => '副号不合规',
        500002 => '副号未实名',
        500003 => '已经设置过副号了，不能重复设置',
        500004 => '不能设置重复的副号',
        500005 => '副号不能和主号重复',
        600001 => '用户已被添加为小助手',
        600002 => '找不到用户',
        9410000 => '直播间列表为空',
        9410001 => '获取房间失败',
        9410002 => '获取商品失败',
        9410003 => '获取回放失败',
    ];

    /**
     * 类型列表
     *
     * @return array
     */
    public function typeList()
    {
        return [0 => '手机直播', 1 => '推流'];
    }

    public function statusList()
    {
        return [101 => '直播中', 102 => '未开始', 103 => '已结束', 104 => '禁播', 105 => '暂停', 106 => '异常', 107 => '已过期'];
    }
}
