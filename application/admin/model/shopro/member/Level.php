<?php

namespace app\admin\model\shopro\member;

use think\Model;

/**
 * 会员等级配置模型
 */
class Level extends Model
{
    // 表名
    protected $name = 'shopro_member_level';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 追加属性
    protected $append = [
        'benefits_text',
        'status_text'
    ];

    /**
     * 状态列表
     */
    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'hidden' => __('Hidden')];
    }

    /**
     * 状态文本获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 权益配置获取器
     */
    public function getBenefitsAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 权益配置设置器
     */
    public function setBenefitsAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 权益文本获取器
     */
    public function getBenefitsTextAttr($value, $data)
    {
        $benefits = $this->getBenefitsAttr($data['benefits'] ?? '');
        $text = [];
        
        if (isset($benefits['free_shipping'])) {
            $shipping = $benefits['free_shipping'];
            $count = $shipping['count'] ?? 0;
            $weight = $shipping['weight_limit'] ?? 0;
            $type = $shipping['type'] ?? 'express';
            
            if ($count == 999) {
                $text[] = "免{$weight}KG单包" . ($type == 'all' ? '快递费或送货费' : '快递费') . '(无限次)';
            } else {
                $text[] = "免{$weight}KG单包" . ($type == 'all' ? '快递费或送货费' : '快递费') . "{$count}次/月";
            }
        }
        
        if (isset($benefits['discount']) && $benefits['discount']['rate'] < 100) {
            $rate = $benefits['discount']['rate'];
            $text[] = "全单{$rate}折";
        }
        
        if (isset($benefits['lottery'])) {
            $extra = $benefits['lottery']['extra_count'] ?? 0;
            if ($extra > 0) {
                $text[] = "加{$extra}次转盘抽奖次数/月";
            }
        }
        
        return implode('，', $text);
    }

    /**
     * 根据消费分获取对应等级
     */
    public static function getLevelByConsumeScore($consumeScore)
    {
        return self::where('consume_score', '<=', $consumeScore)
                   ->where('status', 'normal')
                   ->order('level desc')
                   ->find();
    }

    /**
     * 获取所有等级列表
     */
    public static function getAllLevels()
    {
        return self::where('status', 'normal')
                   ->order('level asc')
                   ->select();
    }

    /**
     * 获取下一等级
     */
    public function getNextLevel()
    {
        return self::where('level', '>', $this->level)
                   ->where('status', 'normal')
                   ->order('level asc')
                   ->find();
    }

    /**
     * 获取等级权益配置
     */
    public function getBenefitConfig($type = null)
    {
        $benefits = $this->benefits;
        
        if ($type) {
            return isset($benefits[$type]) ? $benefits[$type] : [];
        }
        
        return $benefits;
    }

    /**
     * 检查是否有免邮权益
     */
    public function hasFreeShipping()
    {
        $benefits = $this->benefits;
        return isset($benefits['free_shipping']) && $benefits['free_shipping']['count'] > 0;
    }

    /**
     * 检查是否有折扣权益
     */
    public function hasDiscount()
    {
        $benefits = $this->benefits;
        return isset($benefits['discount']) && $benefits['discount']['rate'] < 100;
    }

    /**
     * 获取折扣率
     */
    public function getDiscountRate()
    {
        $benefits = $this->benefits;
        return isset($benefits['discount']) ? $benefits['discount']['rate'] : 100;
    }

    /**
     * 检查是否有转盘权益
     */
    public function hasLotteryBenefit()
    {
        $benefits = $this->benefits;
        return isset($benefits['lottery']) && $benefits['lottery']['extra_count'] > 0;
    }

    /**
     * 获取转盘额外次数
     */
    public function getLotteryExtraCount()
    {
        $benefits = $this->benefits;
        return isset($benefits['lottery']) ? $benefits['lottery']['extra_count'] : 0;
    }
}
