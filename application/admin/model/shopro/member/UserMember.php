<?php

namespace app\admin\model\shopro\member;

use think\Model;
use app\admin\model\shopro\member\Level;
use app\admin\model\User;

/**
 * 用户会员等级记录模型
 */
class UserMember extends Model
{
    // 表名
    protected $name = 'shopro_user_member';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 追加属性
    protected $append = [
        'status_text',
        'level_name',
        'next_level_info',
        'progress_percent'
    ];

    /**
     * 关联用户表
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联等级表
     */
    public function memberLevel()
    {
        return $this->belongsTo(Level::class, 'level_id');
    }

    /**
     * 状态列表
     */
    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'expired' => __('Expired')];
    }

    /**
     * 状态文本获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 等级名称获取器
     */
    public function getLevelNameAttr($value, $data)
    {
        if ($data['level'] == 0) {
            return '普通会员';
        }
        
        $level = Level::find($data['level_id'] ?? 0);
        return $level ? $level->name : 'VIP' . $data['level'];
    }

    /**
     * 下一等级信息获取器
     */
    public function getNextLevelInfoAttr($value, $data)
    {
        $nextLevel = Level::where('consume_score', '>', $data['consume_score'])
                          ->where('status', 'normal')
                          ->order('consume_score asc')
                          ->find();
        
        if (!$nextLevel) {
            return null;
        }
        
        return [
            'id' => $nextLevel->id,
            'name' => $nextLevel->name,
            'level' => $nextLevel->level,
            'consume_score' => $nextLevel->consume_score,
            'need_score' => $nextLevel->consume_score - $data['consume_score'],
            'benefits' => $nextLevel->benefits_text
        ];
    }

    /**
     * 升级进度百分比获取器
     */
    public function getProgressPercentAttr($value, $data)
    {
        $nextLevelInfo = $this->getNextLevelInfoAttr($value, $data);
        
        if (!$nextLevelInfo) {
            return 100; // 已达到最高等级
        }
        
        $currentScore = $data['consume_score'];
        $currentLevelScore = 0;
        
        // 获取当前等级的消费分要求
        if ($data['level'] > 0) {
            $currentLevel = Level::find($data['level_id']);
            $currentLevelScore = $currentLevel ? $currentLevel->consume_score : 0;
        }
        
        $nextLevelScore = $nextLevelInfo['consume_score'];
        $totalNeed = $nextLevelScore - $currentLevelScore;
        $currentProgress = $currentScore - $currentLevelScore;
        
        return $totalNeed > 0 ? min(100, round(($currentProgress / $totalNeed) * 100, 2)) : 0;
    }

    /**
     * 根据用户ID获取会员信息
     */
    public static function getMemberByUserId($userId)
    {
        $member = self::where('user_id', $userId)->find();
        
        if (!$member) {
            // 创建默认会员记录
            $member = self::create([
                'user_id' => $userId,
                'level_id' => 0,
                'level' => 0,
                'consume_score' => 0,
                'total_recharge' => 0,
                'total_consume' => 0,
                'status' => 'normal'
            ]);
        }
        
        return $member;
    }

    /**
     * 更新消费分并检查等级升级
     */
    public function updateConsumeScore($amount, $type = 'consume', $triggerId = null)
    {
        $oldLevel = $this->level;
        $oldLevelId = $this->level_id;
        
        // 更新消费分
        $this->consume_score += $amount;
        
        // 更新对应的金额字段
        if ($type == 'recharge') {
            $this->total_recharge += $amount;
        } else {
            $this->total_consume += $amount;
        }
        
        // 检查等级升级
        $newLevel = Level::getLevelByConsumeScore($this->consume_score);
        
        if ($newLevel && $newLevel->level > $this->level) {
            $this->level_id = $newLevel->id;
            $this->level = $newLevel->level;
            $this->upgrade_time = time();
            
            // 记录升级日志
            $this->logUpgrade($oldLevel, $newLevel->level, $oldLevelId, $newLevel->id, $type, $triggerId);
        }
        
        $this->save();
        
        return $this;
    }

    /**
     * 记录等级升级日志
     */
    private function logUpgrade($fromLevel, $toLevel, $fromLevelId, $toLevelId, $triggerType, $triggerId)
    {
        $upgradeLog = new \app\admin\model\shopro\member\UpgradeLog();
        $upgradeLog->save([
            'user_id' => $this->user_id,
            'from_level' => $fromLevel,
            'to_level' => $toLevel,
            'from_level_id' => $fromLevelId,
            'to_level_id' => $toLevelId,
            'consume_score' => $this->consume_score,
            'trigger_type' => $triggerType,
            'trigger_id' => $triggerId,
            'remark' => "从{$fromLevel}级升级到{$toLevel}级"
        ]);
    }

    /**
     * 获取当前等级权益
     */
    public function getCurrentBenefits()
    {
        if ($this->level == 0) {
            return [];
        }
        
        $level = Level::find($this->level_id);
        return $level ? $level->benefits : [];
    }

    /**
     * 检查是否有某项权益
     */
    public function hasBenefit($type)
    {
        $benefits = $this->getCurrentBenefits();
        return isset($benefits[$type]);
    }

    /**
     * 获取权益配置
     */
    public function getBenefitConfig($type)
    {
        $benefits = $this->getCurrentBenefits();
        return isset($benefits[$type]) ? $benefits[$type] : [];
    }

    /**
     * 获取当月权益使用情况
     */
    public function getMonthlyBenefitUsage($benefitType, $month = null)
    {
        if (!$month) {
            $month = date('Y-m');
        }
        
        $benefitLog = new \app\admin\model\shopro\member\BenefitLog();
        return $benefitLog->where('user_id', $this->user_id)
                         ->where('benefit_type', $benefitType)
                         ->where('month', $month)
                         ->sum('used_count');
    }

    /**
     * 检查权益是否可用
     */
    public function canUseBenefit($benefitType, $month = null)
    {
        $config = $this->getBenefitConfig($benefitType);
        if (empty($config)) {
            return false;
        }

        $totalCount = $config['count'] ?? 0;
        if ($totalCount == 999) {
            return true; // 无限次使用
        }

        $usedCount = $this->getMonthlyBenefitUsage($benefitType, $month);
        return $usedCount < $totalCount;
    }

    /**
     * 使用权益
     */
    public function useBenefit($benefitType, $orderId = null, $remark = '')
    {
        if (!$this->canUseBenefit($benefitType)) {
            return false;
        }

        $month = date('Y-m');
        $benefitLog = new \app\admin\model\shopro\member\BenefitLog();

        // 查找或创建当月权益记录
        $log = $benefitLog->where('user_id', $this->user_id)
                         ->where('benefit_type', $benefitType)
                         ->where('month', $month)
                         ->find();

        if (!$log) {
            $config = $this->getBenefitConfig($benefitType);
            $log = $benefitLog->create([
                'user_id' => $this->user_id,
                'level_id' => $this->level_id,
                'benefit_type' => $benefitType,
                'benefit_name' => $this->getBenefitName($benefitType),
                'used_count' => 0,
                'total_count' => $config['count'] ?? 0,
                'month' => $month,
                'expire_time' => strtotime(date('Y-m-t 23:59:59')),
                'status' => 'normal'
            ]);
        }

        // 增加使用次数
        $log->used_count += 1;
        if ($orderId) {
            $log->order_id = $orderId;
        }
        if ($remark) {
            $log->remark = $remark;
        }

        $log->save();

        return true;
    }

    /**
     * 获取权益名称
     */
    private function getBenefitName($benefitType)
    {
        $names = [
            'free_shipping' => '免邮权益',
            'discount' => '折扣权益',
            'lottery' => '转盘权益'
        ];

        return $names[$benefitType] ?? $benefitType;
    }
}
}
