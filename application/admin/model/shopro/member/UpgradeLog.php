<?php

namespace app\admin\model\shopro\member;

use think\Model;
use app\admin\model\User;
use app\admin\model\shopro\member\Level;

/**
 * 会员等级升级记录模型
 */
class UpgradeLog extends Model
{
    // 表名
    protected $name = 'shopro_member_upgrade_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false; // 升级记录不需要更新时间

    // 追加属性
    protected $append = [
        'trigger_type_text',
        'from_level_name',
        'to_level_name'
    ];

    /**
     * 关联用户表
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联原等级表
     */
    public function fromLevel()
    {
        return $this->belongsTo(Level::class, 'from_level_id');
    }

    /**
     * 关联新等级表
     */
    public function toLevel()
    {
        return $this->belongsTo(Level::class, 'to_level_id');
    }

    /**
     * 触发类型列表
     */
    public function getTriggerTypeList()
    {
        return [
            'consume' => '消费升级',
            'recharge' => '充值升级',
            'manual' => '手动调整',
            'system' => '系统调整'
        ];
    }

    /**
     * 触发类型文本获取器
     */
    public function getTriggerTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['trigger_type']) ? $data['trigger_type'] : '');
        $list = $this->getTriggerTypeList();
        return isset($list[$value]) ? $list[$value] : $value;
    }

    /**
     * 原等级名称获取器
     */
    public function getFromLevelNameAttr($value, $data)
    {
        if ($data['from_level'] == 0) {
            return '普通会员';
        }
        
        $level = Level::find($data['from_level_id'] ?? 0);
        return $level ? $level->name : 'VIP' . $data['from_level'];
    }

    /**
     * 新等级名称获取器
     */
    public function getToLevelNameAttr($value, $data)
    {
        if ($data['to_level'] == 0) {
            return '普通会员';
        }
        
        $level = Level::find($data['to_level_id'] ?? 0);
        return $level ? $level->name : 'VIP' . $data['to_level'];
    }

    /**
     * 获取用户升级历史
     */
    public static function getUserUpgradeHistory($userId, $limit = 10)
    {
        return self::where('user_id', $userId)
                   ->order('createtime desc')
                   ->limit($limit)
                   ->select();
    }

    /**
     * 获取升级统计
     */
    public static function getUpgradeStats($startTime = null, $endTime = null)
    {
        $query = self::field('to_level, COUNT(*) as count')
                    ->group('to_level');
        
        if ($startTime) {
            $query->where('createtime', '>=', $startTime);
        }
        
        if ($endTime) {
            $query->where('createtime', '<=', $endTime);
        }
        
        return $query->select();
    }

    /**
     * 记录升级日志
     */
    public static function logUpgrade($userId, $fromLevel, $toLevel, $fromLevelId, $toLevelId, $consumeScore, $triggerType = 'consume', $triggerId = null, $remark = '')
    {
        return self::create([
            'user_id' => $userId,
            'from_level' => $fromLevel,
            'to_level' => $toLevel,
            'from_level_id' => $fromLevelId,
            'to_level_id' => $toLevelId,
            'consume_score' => $consumeScore,
            'trigger_type' => $triggerType,
            'trigger_id' => $triggerId,
            'remark' => $remark ?: "从{$fromLevel}级升级到{$toLevel}级"
        ]);
    }

    /**
     * 获取最近升级的用户
     */
    public static function getRecentUpgrades($limit = 20)
    {
        return self::with(['user', 'toLevel'])
                   ->order('createtime desc')
                   ->limit($limit)
                   ->select();
    }

    /**
     * 获取某个时间段的升级趋势
     */
    public static function getUpgradeTrend($days = 30)
    {
        $startTime = time() - ($days * 24 * 3600);
        
        return self::field('DATE(FROM_UNIXTIME(createtime)) as date, COUNT(*) as count')
                   ->where('createtime', '>=', $startTime)
                   ->group('date')
                   ->order('date asc')
                   ->select();
    }
}
