<?php

namespace app\admin\model\shopro\member;

use think\Model;
use app\admin\model\User;
use app\admin\model\shopro\member\Level;

/**
 * 会员权益使用记录模型
 */
class BenefitLog extends Model
{
    // 表名
    protected $name = 'shopro_member_benefit_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 追加属性
    protected $append = [
        'status_text',
        'benefit_type_text',
        'usage_rate'
    ];

    /**
     * 关联用户表
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联等级表
     */
    public function memberLevel()
    {
        return $this->belongsTo(Level::class, 'level_id');
    }

    /**
     * 状态列表
     */
    public function getStatusList()
    {
        return [
            'normal' => __('Normal'), 
            'used' => __('Used'), 
            'expired' => __('Expired')
        ];
    }

    /**
     * 权益类型列表
     */
    public function getBenefitTypeList()
    {
        return [
            'free_shipping' => '免邮权益',
            'discount' => '折扣权益',
            'lottery' => '转盘权益'
        ];
    }

    /**
     * 状态文本获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 权益类型文本获取器
     */
    public function getBenefitTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['benefit_type']) ? $data['benefit_type'] : '');
        $list = $this->getBenefitTypeList();
        return isset($list[$value]) ? $list[$value] : $value;
    }

    /**
     * 使用率获取器
     */
    public function getUsageRateAttr($value, $data)
    {
        $used = $data['used_count'] ?? 0;
        $total = $data['total_count'] ?? 0;
        
        if ($total == 0) {
            return 0;
        }
        
        if ($total == 999) {
            return $used > 0 ? 100 : 0; // 无限次使用
        }
        
        return round(($used / $total) * 100, 2);
    }

    /**
     * 获取用户某月的权益使用统计
     */
    public static function getUserMonthlyStats($userId, $month = null)
    {
        if (!$month) {
            $month = date('Y-m');
        }
        
        return self::where('user_id', $userId)
                   ->where('month', $month)
                   ->select();
    }

    /**
     * 获取用户某类型权益的使用情况
     */
    public static function getUserBenefitUsage($userId, $benefitType, $month = null)
    {
        if (!$month) {
            $month = date('Y-m');
        }
        
        return self::where('user_id', $userId)
                   ->where('benefit_type', $benefitType)
                   ->where('month', $month)
                   ->find();
    }

    /**
     * 检查权益是否已用完
     */
    public function isUsedUp()
    {
        if ($this->total_count == 999) {
            return false; // 无限次使用
        }
        
        return $this->used_count >= $this->total_count;
    }

    /**
     * 获取剩余次数
     */
    public function getRemainingCount()
    {
        if ($this->total_count == 999) {
            return 999; // 无限次使用
        }
        
        return max(0, $this->total_count - $this->used_count);
    }

    /**
     * 批量创建用户当月权益记录
     */
    public static function createMonthlyBenefits($userId, $levelId, $benefits, $month = null)
    {
        if (!$month) {
            $month = date('Y-m');
        }
        
        $expireTime = strtotime(date('Y-m-t 23:59:59', strtotime($month . '-01')));
        $logs = [];
        
        foreach ($benefits as $type => $config) {
            if (!isset($config['count']) || $config['count'] <= 0) {
                continue;
            }
            
            // 检查是否已存在
            $exists = self::where('user_id', $userId)
                         ->where('benefit_type', $type)
                         ->where('month', $month)
                         ->find();
            
            if (!$exists) {
                $logs[] = [
                    'user_id' => $userId,
                    'level_id' => $levelId,
                    'benefit_type' => $type,
                    'benefit_name' => self::getBenefitTypeName($type),
                    'used_count' => 0,
                    'total_count' => $config['count'],
                    'month' => $month,
                    'expire_time' => $expireTime,
                    'status' => 'normal',
                    'createtime' => time(),
                    'updatetime' => time()
                ];
            }
        }
        
        if (!empty($logs)) {
            self::insertAll($logs);
        }
        
        return count($logs);
    }

    /**
     * 获取权益类型名称
     */
    private static function getBenefitTypeName($type)
    {
        $names = [
            'free_shipping' => '免邮权益',
            'discount' => '折扣权益',
            'lottery' => '转盘权益'
        ];
        
        return $names[$type] ?? $type;
    }

    /**
     * 清理过期权益记录
     */
    public static function cleanExpiredBenefits()
    {
        $expiredCount = self::where('expire_time', '<', time())
                           ->where('status', 'normal')
                           ->update(['status' => 'expired']);
        
        return $expiredCount;
    }
}
