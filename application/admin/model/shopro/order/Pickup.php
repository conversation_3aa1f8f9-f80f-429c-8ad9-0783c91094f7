<?php

namespace app\admin\model\shopro\order;

use think\Model;

class Pickup extends Model
{
    protected $name = 'shopro_order_pickup';
 
    // 开启自动写入时间戳
    protected $autoWriteTimestamp = 'int';
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [
        'pickup_time_text',
        'verify_time_text',
        'is_verified_text'
    ];

    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo('app\admin\model\shopro\order\Order', 'order_id', 'id');
    }

    /**
     * 关联自提点
     */
    public function pickupPoint()
    {
        return $this->belongsTo('app\admin\model\Pickup', 'pickup_id', 'id');
    }

    /**
     * 获取取货时间文本
     */
    public function getPickupTimeTextAttr($value, $data)
    {
        $pickup_time = $data['pickup_time'] ?? 0;
        if (!$pickup_time) {
            return '-';
        }
        return date('Y-m-d H:i:s', $pickup_time);
    }

    /**
     * 获取核销时间文本
     */
    public function getVerifyTimeTextAttr($value, $data)
    {
        $verify_time = $data['verify_time'] ?? 0;
        if (!$verify_time) {
            return '-';
        }
        return date('Y-m-d H:i:s', $verify_time);
    }

    /**
     * 获取是否已核销文本
     */
    public function getIsVerifiedTextAttr($value, $data)
    {
        $is_verified = $data['is_verified'] ?? 0;
        return $is_verified ? '已核销' : '未核销';
    }
} 