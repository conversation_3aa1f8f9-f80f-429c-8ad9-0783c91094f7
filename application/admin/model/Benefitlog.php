<?php

namespace app\admin\model;

use think\Model;


class Benefitlog extends Model
{

    

    

    // 表名
    protected $name = 'benefitlog';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'expire_time_text',
        'status_text'
    ];
    

    
    public function getStatusList()
    {
        return ['0' => __('过期'), '1' => __('正常'), '2' => __('已用完')];
    }


    public function getExpireTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['expire_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    protected function setExpireTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function memberlevel()
    {
        return $this->belongsTo('Memberlevel', 'level_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 获取用户某月的权益使用统计
     */
    public static function getUserMonthlyStats($userId, $month = null)
    {
        if (!$month) {
            $month = date('Y-m');
        }

        return self::where('user_id', $userId)
                   ->where('month', $month)
                   ->select();
    }

    /**
     * 获取用户某类型权益的使用情况
     */
    public static function getUserBenefitUsage($userId, $benefitType, $month = null)
    {
        if (!$month) {
            $month = date('Y-m');
        }

        return self::where('user_id', $userId)
                   ->where('benefit_type', $benefitType)
                   ->where('month', $month)
                   ->find();
    }

    /**
     * 检查权益是否已用完
     */
    public function isUsedUp()
    {
        if ($this->total_count == 999) {
            return false; // 无限次使用
        }

        return $this->used_count >= $this->total_count;
    }

    /**
     * 获取剩余次数
     */
    public function getRemainingCount()
    {
        if ($this->total_count == 999) {
            return 999; // 无限次使用
        }

        return max(0, $this->total_count - $this->used_count);
    }

}
