<?php

namespace app\admin\model;

use think\Model;


class Memberlevel extends Model
{

    

    

    // 表名
    protected $name = 'memberlevel';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text',
        'benefits_text'
    ];
    

    protected static function init()
    {
        self::afterInsert(function ($row) {
            if (!$row['weigh']) {
                $pk = $row->getPk();
                $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
            }
        });
    }

    
    public function getStatusList()
    {
        return ['0' => __('隐藏'), '1' => __('显示')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    /**
     * 权益配置获取器
     */
    public function getBenefitsAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 权益配置设置器
     */
    public function setBenefitsAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 权益文本获取器
     */
    public function getBenefitsTextAttr($value, $data)
    {
        $benefits = $this->getBenefitsAttr($data['benefits'] ?? '');
        $text = [];

        if (isset($benefits['free_shipping'])) {
            $shipping = $benefits['free_shipping'];
            $count = $shipping['count'] ?? 0;
            $weight = $shipping['weight_limit'] ?? 0;
            $type = $shipping['type'] ?? 'express';

            if ($count == 999) {
                $text[] = "免{$weight}KG单包" . ($type == 'all' ? '快递费或送货费' : '快递费') . '(无限次)';
            } else {
                $text[] = "免{$weight}KG单包" . ($type == 'all' ? '快递费或送货费' : '快递费') . "{$count}次/月";
            }
        }

        if (isset($benefits['discount']) && $benefits['discount']['rate'] < 100) {
            $rate = $benefits['discount']['rate'];
            $text[] = "全单{$rate}折";
        }

        if (isset($benefits['lottery'])) {
            $extra = $benefits['lottery']['extra_count'] ?? 0;
            if ($extra > 0) {
                $text[] = "加{$extra}次转盘抽奖次数/月";
            }
        }

        return implode('，', $text);
    }

    /**
     * 根据消费分获取对应等级
     */
    public static function getLevelByConsumeScore($consumeScore)
    {
        return self::where('consume_score', '<=', $consumeScore)
                   ->where('status', '1')
                   ->order('level desc')
                   ->find();
    }

    /**
     * 获取所有显示的等级列表
     */
    public static function getAllLevels()
    {
        return self::where('status', '1')
                   ->order('level asc')
                   ->select();
    }

    /**
     * 获取下一等级
     */
    public function getNextLevel()
    {
        return self::where('level', '>', $this->level)
                   ->where('status', '1')
                   ->order('level asc')
                   ->find();
    }

}
