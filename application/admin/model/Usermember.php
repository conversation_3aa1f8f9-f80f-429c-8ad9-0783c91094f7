<?php

namespace app\admin\model;

use think\Model;


class Usermember extends Model
{

    

    

    // 表名
    protected $name = 'usermember';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'upgrade_time_text',
        'expire_time_text',
        'status_text',
        'level_name',
        'next_level_info',
        'progress_percent'
    ];
    

    
    public function getStatusList()
    {
        return ['0' => __('过期'), '1' => __('正常')];
    }


    public function getUpgradeTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['upgrade_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getExpireTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['expire_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    protected function setUpgradeTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setExpireTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function memberlevel()
    {
        return $this->belongsTo('Memberlevel', 'level_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 等级名称获取器
     */
    public function getLevelNameAttr($value, $data)
    {
        if ($data['level'] == 0) {
            return '普通会员';
        }

        $level = \app\admin\model\Memberlevel::find($data['level_id'] ?? 0);
        return $level ? $level->name : 'VIP' . $data['level'];
    }

    /**
     * 下一等级信息获取器
     */
    public function getNextLevelInfoAttr($value, $data)
    {
        $nextLevel = \app\admin\model\Memberlevel::where('consume_score', '>', $data['consume_score'])
                          ->where('status', '1')
                          ->order('consume_score asc')
                          ->find();

        if (!$nextLevel) {
            return null;
        }

        return [
            'id' => $nextLevel->id,
            'name' => $nextLevel->name,
            'level' => $nextLevel->level,
            'consume_score' => $nextLevel->consume_score,
            'need_score' => $nextLevel->consume_score - $data['consume_score'],
            'benefits' => $nextLevel->benefits_text
        ];
    }

    /**
     * 升级进度百分比获取器
     */
    public function getProgressPercentAttr($value, $data)
    {
        $nextLevelInfo = $this->getNextLevelInfoAttr($value, $data);

        if (!$nextLevelInfo) {
            return 100; // 已达到最高等级
        }

        $currentScore = $data['consume_score'];
        $currentLevelScore = 0;

        // 获取当前等级的消费分要求
        if ($data['level'] > 0) {
            $currentLevel = \app\admin\model\Memberlevel::find($data['level_id']);
            $currentLevelScore = $currentLevel ? $currentLevel->consume_score : 0;
        }

        $nextLevelScore = $nextLevelInfo['consume_score'];
        $totalNeed = $nextLevelScore - $currentLevelScore;
        $currentProgress = $currentScore - $currentLevelScore;

        return $totalNeed > 0 ? min(100, round(($currentProgress / $totalNeed) * 100, 2)) : 0;
    }

    /**
     * 根据用户ID获取会员信息
     */
    public static function getMemberByUserId($userId)
    {
        $member = self::where('user_id', $userId)->find();

        if (!$member) {
            // 创建默认会员记录
            $member = self::create([
                'user_id' => $userId,
                'level_id' => 0,
                'level' => 0,
                'consume_score' => 0,
                'total_recharge' => 0,
                'total_consume' => 0,
                'status' => '1'
            ]);
        }

        return $member;
    }

    /**
     * 更新消费分并检查等级升级
     */
    public function updateConsumeScore($amount, $type = 'consume', $triggerId = null)
    {
        $oldLevel = $this->level;
        $oldLevelId = $this->level_id;

        // 更新消费分
        $this->consume_score += $amount;

        // 更新对应的金额字段
        if ($type == 'recharge') {
            $this->total_recharge += $amount;
        } else {
            $this->total_consume += $amount;
        }

        // 检查等级升级
        $newLevel = \app\admin\model\Memberlevel::getLevelByConsumeScore($this->consume_score);

        if ($newLevel && $newLevel->level > $this->level) {
            $this->level_id = $newLevel->id;
            $this->level = $newLevel->level;
            $this->upgrade_time = time();

            // 记录升级日志
            $this->logUpgrade($oldLevel, $newLevel->level, $oldLevelId, $newLevel->id, $type, $triggerId);
        }

        $this->save();

        return $this;
    }

    /**
     * 记录等级升级日志
     */
    private function logUpgrade($fromLevel, $toLevel, $fromLevelId, $toLevelId, $triggerType, $triggerId)
    {
        $upgradeLog = new \app\admin\model\Upgradelog();
        $upgradeLog->save([
            'user_id' => $this->user_id,
            'from_level' => $fromLevel,
            'to_level' => $toLevel,
            'from_level_id' => $fromLevelId,
            'to_level_id' => $toLevelId,
            'consume_score' => $this->consume_score,
            'trigger_type' => $triggerType,
            'trigger_id' => $triggerId,
            'remark' => "从{$fromLevel}级升级到{$toLevel}级"
        ]);
    }

    /**
     * 获取当前等级权益
     */
    public function getCurrentBenefits()
    {
        if ($this->level == 0) {
            return [];
        }

        $level = \app\admin\model\Memberlevel::find($this->level_id);
        if (!$level) {
            return [];
        }

        // 处理benefits字段（可能是字符串或数组）
        if (is_string($level->benefits)) {
            return json_decode($level->benefits, true) ?: [];
        } else {
            return $level->benefits ?: [];
        }
    }

}
