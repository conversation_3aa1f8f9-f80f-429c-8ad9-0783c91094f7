<?php

namespace app\admin\model;

use think\Model;


class Lotteryrecord extends Model
{

    

    

    // 表名
    protected $name = 'lotteryrecord';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'choice_type_text',
        'delivery_status_text',
        'delivery_time_text'
    ];
    

    
    public function getChoiceTypeList()
    {
        return ['goods' => __('Choice_type goods'), 'score' => __('Choice_type score')];
    }

    public function getDeliveryStatusList()
    {
        return ['pending' => __('Delivery_status pending'), 'processing' => __('Delivery_status processing'), 'shipped' => __('Delivery_status shipped'), 'completed' => __('Delivery_status completed')];
    }


    public function getChoiceTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['choice_type'] ?? '');
        $list = $this->getChoiceTypeList();
        return $list[$value] ?? '';
    }


    public function getDeliveryStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['delivery_status'] ?? '');
        $list = $this->getDeliveryStatusList();
        return $list[$value] ?? '';
    }


    public function getDeliveryTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['delivery_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setDeliveryTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function lotteryprize()
    {
        return $this->belongsTo('Lotteryprize', 'prize_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
