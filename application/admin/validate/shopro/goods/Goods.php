<?php

namespace app\admin\validate\shopro\goods;

use think\Validate;
use app\admin\validate\shopro\traits\CustomRule;

class Goods extends Validate
{
    use CustomRule;

    protected $rule = [
        'title' => 'require', 
        // 'subtitle' => 'require', 
        'category_ids' => 'require', 
        'image' => 'require', 
        'images' => 'require|array',
        'is_sku' => 'require',
        // 'cost_price' => 'require', 
        // 'original_price' => 'requireIf:is_sku,0', 
        'price' => 'requireIf:is_sku,0', 
        'dispatch_id' => 'requireIfSendType:0', 
        'shang_id' => 'requireIfSendType:1', 
    ];

    protected $message  =   [
        'title.require'     => '请填写商品标题',
        // 'subtitle.require'     => '请填写商品副标题',
        'category_ids.require'     => '请选择商品分类',
        'image.require'     => '请选择商品封面图',
        'images.require'     => '请选择商品轮播图',
        'images.array'     => '请选择商品轮播图',
        // 'cost_price.require'     => '请填写商品成本价',
        // 'original_price.require'     => '请填写商品原价',
        'price.requireIf'     => '请填写商品现价',
        'dispatch_id.requireIfSendType'     => '请选择物流快递模板',
        'shang_id.requireIfSendType'     => '请选择上门配送模板',
    ];

    protected $scene = [
        'add'  =>  ['title', 'image', 'images', 'price', 'dispatch_id', 'shang_id'],
        'sku_params' => ['price']
    ];

    /**
     * 自定义验证规则：根据发货方式验证配送模板
     */
    protected function requireIfSendType($value, $rule, $data)
    {
        // 如果没有选择发货方式，不验证
        if (empty($data['send_type'])) {
            return true;
        }
        
        // 将send_type转换为数组
        $sendTypes = explode(',', $data['send_type']);
        
        // 检查是否包含对应的发货方式
        if (in_array($rule, $sendTypes)) {
            // 如果包含该发货方式，则对应的模板ID必填
            if ($rule == '0' && empty($value)) {
                return false;
            }
            if ($rule == '1' && empty($value)) {
                return false;
            }
        }
        
        return true;
    }
}
