<?php

namespace app\admin\validate\shopro\dispatch;

use think\Validate;

class DispatchExpress extends Validate
{
    protected $rule = [
        'dispatch_id' => 'require',
        'type' => 'require',
        'first_num' => 'require|number',
        'first_price' => 'require|float',
        'additional_num' => 'require|number',
        'additional_price' => 'require|float',
        'free_shipping_num' => 'number',
        'free_shipping_weight' => 'float',
        'province_ids' => 'require',
        'city_ids' => 'require',
        'district_ids' => 'require',
        'is_shang' => 'require|in:0,1',
    ];
} 