<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" min="0" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Level_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-level_id" data-rule="required" min="0" data-source="memberlevel/index" class="form-control selectpage" name="row[level_id]" type="text" value="{$row.level_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Benefit_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-benefit_type" data-rule="required" class="form-control" name="row[benefit_type]" type="text" value="{$row.benefit_type|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Benefit_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-benefit_name" data-rule="required" class="form-control" name="row[benefit_name]" type="text" value="{$row.benefit_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Used_count')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-used_count" data-rule="required" min="0" class="form-control" name="row[used_count]" type="number" value="{$row.used_count|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_count')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_count" data-rule="required" min="0" class="form-control" name="row[total_count]" type="number" value="{$row.total_count|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_id" min="0" data-rule="required" data-source="order/index" class="form-control selectpage" name="row[order_id]" type="text" value="{$row.order_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Month')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-month" data-rule="required" class="form-control" name="row[month]" type="text" value="{$row.month|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Expire_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-expire_time" min="0" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[expire_time]" type="text" value="{:$row.expire_time?datetime($row.expire_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-remark" data-rule="required" class="form-control" name="row[remark]" type="text" value="{$row.remark|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
