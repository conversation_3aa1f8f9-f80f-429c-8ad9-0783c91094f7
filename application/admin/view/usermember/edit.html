<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" min="0" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Level_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-level_id" data-rule="required" min="0" data-source="memberlevel/index" class="form-control selectpage" name="row[level_id]" type="text" value="{$row.level_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Level')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-level" data-rule="required" min="0" class="form-control" name="row[level]" type="number" value="{$row.level|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Consume_score')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-consume_score" data-rule="required" min="0" class="form-control" name="row[consume_score]" type="number" value="{$row.consume_score|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_recharge')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_recharge" data-rule="required" min="0" class="form-control" step="0.01" name="row[total_recharge]" type="number" value="{$row.total_recharge|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_consume')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_consume" data-rule="required" min="0" class="form-control" step="0.01" name="row[total_consume]" type="number" value="{$row.total_consume|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Upgrade_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-upgrade_time" min="0" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[upgrade_time]" type="text" value="{:$row.upgrade_time?datetime($row.upgrade_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Expire_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-expire_time" min="0" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[expire_time]" type="text" value="{:$row.expire_time?datetime($row.expire_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
