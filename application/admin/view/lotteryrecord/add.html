<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" min="0" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Prize_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-prize_id" data-rule="required" min="0" data-source="lotteryprize/index" class="form-control selectpage" name="row[prize_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Prize_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-prize_name" data-rule="required" class="form-control" name="row[prize_name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Prize_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-prize_image" data-rule="required" class="form-control" size="50" name="row[prize_image]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-prize_image" class="btn btn-danger faupload" data-input-id="c-prize_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-prize_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-prize_image" class="btn btn-primary fachoose" data-input-id="c-prize_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-prize_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-prize_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Score_value')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-score_value" data-rule="required" min="0" class="form-control" name="row[score_value]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Shipping_fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-shipping_fee" data-rule="required" class="form-control" step="0.01" name="row[shipping_fee]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Choice_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-choice_type" data-rule="required" class="form-control selectpicker" name="row[choice_type]">
                {foreach name="choiceTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="goods"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Delivery_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="deliveryStatusList" item="vo"}
            <label for="row[delivery_status]-{$key}"><input id="row[delivery_status]-{$key}" name="row[delivery_status]" type="radio" value="{$key}" {in name="key" value="pending"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Delivery_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-delivery_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[delivery_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Delivery_company')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-delivery_company" data-rule="required" class="form-control" name="row[delivery_company]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Delivery_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-delivery_number" data-rule="required" class="form-control" name="row[delivery_number]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Delivery_remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-delivery_remark" data-rule="required" class="form-control" name="row[delivery_remark]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Receiver_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-receiver_name" data-rule="required" class="form-control" name="row[receiver_name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Receiver_phone')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-receiver_phone" data-rule="required" class="form-control" name="row[receiver_phone]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Receiver_address')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-receiver_address" data-rule="required" class="form-control" name="row[receiver_address]" type="text" value="">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
