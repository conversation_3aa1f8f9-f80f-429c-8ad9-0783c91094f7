{include file="/shopro/common/script" /}

<style>
  .order-index .pay {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }
</style>

<div id="index" class="order-index panel panel-default panel-intro" v-cloak>
  <el-container class="panel-block">
    <el-header class="sa-header">
      <el-tabs class="sa-tabs" v-model="state.filter.data.status" @tab-change="onChangeTab">
        <el-tab-pane v-for="item in type.data.status" :key="item"
          :label="`${item.name}${item.num ? '(' + item.num + ')' : ''}`" :name="item.type"></el-tab-pane>
      </el-tabs>
      <div class="sa-title sa-flex sa-row-between">
        <div class="sa-title-left">
          <div class="left-name">充值订单</div>
          <sa-filter-condition v-model="state.filter" @filter-delete="onChangeFilter">
          </sa-filter-condition>
        </div>
        <div class="sa-title-right">
          <el-button class="sa-button-refresh" icon="RefreshRight" @click="getData"></el-button>
          <el-button class="sa-button-refresh" icon="Search" @click="onOpenFilter"></el-button>
          {if $auth->check('shopro/trade/order/export')}
          <el-button :loading="exportLoading" :disabled="exportLoading" @click="onExport('export')">订单导出
          </el-button>
          {/if}
        </div>
      </div>
    </el-header>
    <el-main class="sa-main">
      <el-table height="100%" class="sa-table" :data="state.data" stripe>
        <el-table-column prop="id" label="ID" min-width="90"> </el-table-column>
        <el-table-column label="订单编号" min-width="260">
          <template #default="scope">
            <div class="sa-flex">
              {{ scope.row.order_sn || '-' }}
              <el-icon class="copy-document" @click="onClipboard(scope.row.order_sn)">
                <copy-document />
              </el-icon>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用户信息" min-width="216">
          <template #default="scope">
            <sa-user-profile :user="scope.row.user" :id="scope.row.user_id"></sa-user-profile>
          </template>
        </el-table-column>
        <el-table-column label="订单总金额" min-width="120">
          <template #default="scope">
            <span class="sa-table-line-1"> {{ scope.row.order_amount || '-' }}元 </span>
          </template>
        </el-table-column>
        <el-table-column label="支付总金额" min-width="120">
          <template #default="scope">
            <span class="sa-table-line-1"> {{ scope.row.pay_fee || '-' }}元 </span>
          </template>
        </el-table-column>
        <el-table-column label="支付状态" min-width="80">
          <template #default="scope">
            <span :class="`sa-color--${statusStyle[scope.row.status]}`">
              {{ scope.row.status_text }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="支付方式" min-width="120">
          <template #default="scope">
            <div v-if="scope.row.pay_type" class="sa-flex">
              <img class="pay" :src="`/assets/addons/shopro/img/${scope.row.pay_type}.png`" />
              <div>{{scope.row.pay_type_text}}</div>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column label="充值时间" width="172">
          <template #default="scope">
            {{ scope.row.createtime || '-' }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" min-width="100">
          <template #default="scope">
            {if $auth->check('shopro/trade/order/detail')}
            <el-button type="primary" link @click="onDetail(scope.row.id)">详情</el-button>
            {/if}
          </template>
        </el-table-column>
      </el-table>
    </el-main>
    <el-footer class="sa-footer sa-flex sa-row-right">
      <sa-pagination v-model="pagination" @pagination-change="getData"></sa-pagination>
    </el-footer>
  </el-container>
  <sa-filter v-model="state.filter" @filter-change="onChangeFilter"></sa-filter>
</div>