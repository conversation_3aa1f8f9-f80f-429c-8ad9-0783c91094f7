{include file="/shopro/common/script" /}

<style>
    .groupon-index .goods-item .goods-title {
        color: var(--el-color-primary);
    }

    .groupon-index .goods-item .goods-num {
        font-size: 12px;
    }

    .groupon-index .avatar {
        margin-right: 4px;
    }
</style>

<div id="index" class="groupon-index panel panel-default panel-intro" v-cloak>
    <el-container class="panel-block">
        <el-header class="sa-header">
            <el-tabs class="sa-tabs" v-model="state.filter.data.status" @tab-change="onChangeTab">
                <el-tab-pane v-for="item in state.statusList" :key="item" :label="item.name" :name="item.type">
                </el-tab-pane>
            </el-tabs>
            <div class="sa-title sa-flex sa-row-between">
                <div class="sa-title-left">
                    <div class="left-name">拼团列表</div>
                    <sa-filter-condition v-model="state.filter" @filter-delete="onChangeFilter">
                    </sa-filter-condition>
                </div>
                <div class="sa-title-right">
                    <el-button class="sa-button-refresh" icon="RefreshRight" @click="getData"></el-button>
                    <el-button class="sa-button-refresh" icon="Search" @click="onOpenFilter"></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="sa-main">
            <el-table height="100%" class="sa-table" :data="state.data" stripe @sort-change="onChangeSort">
                <el-table-column prop="id" label="ID" min-width="90" sortable="custom"> </el-table-column>
                <el-table-column label="拼团商品信息" min-width="240">
                    <template #default="scope">
                        <div v-if="scope.row.goods" class="goods-item sa-flex">
                            <sa-image class="mr-2" :url="scope.row.goods.image" size="40"></sa-image>
                            <div>
                                <div class="goods-title sa-table-line-1" @click="onOpenGoodsDetail(scope.row.goods_id)">
                                    {{ scope.row.goods.title || '-' }}
                                </div>
                                <div class="goods-num"> 成团人数：{{ scope.row.num }} </div>
                            </div>
                        </div>
                        <div v-else>{{scope.row.goods_id}}</div>
                    </template>
                </el-table-column>
                <el-table-column label="开团时间" width="172">
                    <template #default="scope">
                        {{ scope.row.createtime || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="团长" min-width="124">
                    <template #default="scope">
                        <sa-user-profile :user="scope.row.user" :id="scope.row.user_id" />
                    </template>
                </el-table-column>
                <el-table-column label="已参团成员" min-width="180">
                    <template #default="scope">
                        <el-scrollbar>
                            <div class="sa-flex">
                                <template v-for="item in scope.row.groupon_logs" :key="item">
                                    <sa-image class="avatar" :url="item.avatar" size="32" radius="16"></sa-image>
                                </template>
                            </div>
                        </el-scrollbar>
                    </template>
                </el-table-column>
                <el-table-column label="剩余名额" min-width="90">
                    <template #default="scope">
                        {{ scope.row.num - scope.row.current_num }}
                    </template>
                </el-table-column>
                <el-table-column label="组团有效时间" width="172">
                    <template #default="scope">
                        {{ scope.row.finish_time || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="拼团状态" min-width="88">
                    <template #default="scope">
                        <div :class="`sa-color--${state.statusClass[scope.row.status]}`">
                            {{ scope.row.status_text || '-' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" min-width="100">
                    <template #default="scope">
                        {if $auth->check('shopro/activity/groupon/detail')}
                        <el-button type="primary" link @click="onDetail(scope.row.id)">查看详情</el-button>
                        {/if}
                    </template>
                </el-table-column>
            </el-table>
        </el-main>
        <el-footer class="sa-footer sa-flex sa-row-right">
            <sa-pagination v-model="pagination" @pagination-change="getData"></sa-pagination>
        </el-footer>
    </el-container>
    <sa-filter v-model="state.filter" @filter-change="onChangeFilter"></sa-filter>
</div>