{include file="/shopro/common/script" /}

<div id="index" class="activity-index panel panel-default panel-intro" v-cloak>
    <el-container class="panel-block">
        <el-header class="sa-header">
            <div class="sa-title sa-flex sa-row-between">
                <div class="sa-title-left">
                    <div class="left-name">{{state.title}}</div>
                    <sa-filter-condition v-model="state.filter" @filter-delete="onChangeFilter">
                    </sa-filter-condition>
                </div>
                <div class="sa-title-right">
                    <el-button class="sa-button-refresh" icon="RefreshRight" @click="getData"></el-button>
                    <el-button class="sa-button-refresh" icon="Search" @click="onOpenFilter"></el-button>
                    {if $auth->check('shopro/activity/activity/add')}
                    <el-button icon="Plus" type="primary" @click="onAdd">添加</el-button>
                    {/if}
                    {if $auth->check('shopro/activity/activity/recyclebin')}
                    <el-button type="danger" icon="Delete" plain @click="onRecyclebin">回收站</el-button>
                    {/if}
                </div>
            </div>
        </el-header>
        <el-main class="sa-main">
            <el-table height="100%" class="sa-table" :data="state.data" stripe @sort-change="onChangeSort">
                <el-table-column prop="id" label="ID" min-width="90" sortable="custom"> </el-table-column>
                <el-table-column label="活动名称" min-width="200">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{ scope.row.title }}</div>
                    </template>
                </el-table-column>
                <el-table-column v-if="state.type != 'signin'" label="参与商品" min-width="244">
                    <template #default="scope">
                        <template v-if="scope.row.goods_ids">
                            <el-popover width="300" trigger="hover">
                                <template v-if="scope.row.goods.length>0">
                                    <div class="sa-flex" v-for="item in scope.row.goods" :key="item">
                                        <sa-image class="mr-2" :url="item.image" size="40"></sa-image>
                                        <div>
                                            <div class="sa-table-line-1 mb-1">
                                                {{ item.title }}
                                            </div>
                                            <div>#{{ item.id }}</div>
                                        </div>
                                    </div>
                                </template>
                                <div v-else>
                                    #{{scope.row.goods_ids}}
                                </div>
                                <template #reference>
                                    <span class="sa-color--primary">{{scope.row.goods_ids.split(',')?.length }}</span>
                                </template>
                            </el-popover>
                            件商品
                        </template>
                        <div v-if="!scope.row.goods_ids">全部商品</div>
                    </template>
                </el-table-column>
                <el-table-column label="活动状态" min-width="110">
                    <template #default="scope">
                        <div :class="`${state.statusStyle[scope.row.status]}`">
                            {{ scope.row.status_text }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="活动时间" min-width="250">
                    <template #default="scope">
                        <div>
                            <div>开始时间：{{ scope.row.start_time }}</div>
                            <div>结束时间：{{ scope.row.end_time }}</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="updatetime" label="更新时间" width="172"></el-table-column>
                <el-table-column label="活动说明" min-width="158">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{ scope.row.richtext_title || '-'}}</div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="160" fixed="right">
                    <template #default="scope">
                        {if $auth->check('shopro/activity/groupon/index')}
                        <el-button v-if="state.type == 'groupon' || state.type == 'groupon_ladder'" type="primary" link
                            @click="onGroupon(scope.row.id)">查看</el-button>
                        {/if}
                        {if $auth->check('shopro/activity/activity/edit')}
                        <el-button type="primary" link @click="onEdit(scope.row.id)">编辑</el-button>
                        {/if}
                        <el-popconfirm width="fit-content" confirm-button-text="确认" cancel-button-text="取消"
                            title="确认删除这条记录?" @confirm="onDelete(scope.row.id)">
                            <template #reference>
                                {if $auth->check('shopro/activity/activity/delete')}
                                <el-button type="danger" link>删除</el-button>
                                {/if}
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
        </el-main>
        <el-footer class="sa-footer sa-flex sa-row-right">
            <sa-pagination v-model="pagination" @pagination-change="getData"></sa-pagination>
        </el-footer>
    </el-container>
    <sa-filter v-model="state.filter" @filter-change="onChangeFilter"></sa-filter>
</div>