{include file="/shopro/common/script" /}

<div id="select" class="activity-select" v-cloak>
    <el-container class="panel-block">
        <el-main>
            <el-table height="100%" class="sa-table" :data="state.data" stripe>
                <el-table-column prop="id" label="ID" min-width="90"></el-table-column>
                <el-table-column label="名称" min-width="128">
                    <template #default="scope">
                        <div class="sa-table-line-1">
                            {{ scope.row.title }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="类型" min-width="74">
                    <template #default="scope">
                        <div class="sa-table-line-1">
                            {{ scope.row.type_text }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" min-width="120">
                    <template #default="scope">
                        <el-button type="primary" link @click="onConfirm(scope.row)">选择</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-main>
        <el-footer class="sa-footer sa-flex sa-row-right">
            <sa-pagination v-model="pagination" @pagination-change="getData"></sa-pagination>
        </el-footer>
    </el-container>
</div>