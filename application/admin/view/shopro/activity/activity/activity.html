{include file="/shopro/common/script" /}

<style>
    .activity-index .sa-main {
        --el-main-padding: 20px 40px;
    }

    .activity-index .title {
        line-height: 20px;
        font-size: 16px;
        color: var(--sa-title);
        margin-bottom: 12px;
    }

    .activity-index .activity-item {
        width: 250px;
        padding: 18px 20px;
        background: var(--sa-table-header-bg);
        border-radius: 4px;
        margin: 0 16px 16px 0;
        cursor: pointer;
    }

    .activity-index .activity-item:hover {
        transition: width height 0.5s;
        transform: scale(1.05);
    }

    .activity-index .left {
        width: 44px;
        height: 44px;
        background: var(--el-color-primary);
        border-radius: 4px;
        overflow: hidden;
        margin-right: 12px;
    }

    .activity-index .left img {
        width: 100%;
        height: 100%;
    }

    .activity-index .activity-title {
        line-height: 18px;
        font-size: 14px;
        font-weight: 600;
        color: var(--sa-subtitle);
        margin: 2px 0 6px;
    }

    .activity-index .activity-subtitle {
        line-height: 16px;
        font-size: 12px;
        color: var(--sa-subfont);
    }
</style>

<div id="index" class="activity-index panel panel-default panel-intro" v-cloak>
    <el-container class="panel-block">
        <el-header class="sa-header">
            <div class="sa-title sa-flex">
                <div class="sa-title-left">
                    <div class="left-name">营销活动</div>
                </div>
            </div>
        </el-header>
        <el-main class="sa-main">
            <template v-for="item in activityData">
                <div class="title">
                    {{item.title}}
                </div>
                <div class="sa-flex sa-flex-wrap">
                    <div class="activity-item sa-flex" v-for="(value,key) in item.children"
                        @click="onActivity(key,value.title)">
                        <div class="left">
                            <img :src="`/assets/addons/shopro/img/activity/${key}.png`" />
                        </div>
                        <div>
                            <div class="activity-title">{{value.title}}</div>
                            <div class="activity-subtitle">{{value.subtitle}}</div>
                        </div>
                    </div>
                </div>
            </template>
        </el-main>
    </el-container>
</div>