{include file="/shopro/common/script" /}

<style>
    .aftersale-index .sa-table {
        font-size: 12px;
    }

    .aftersale-index .sa-table-wrap {
        height: 100%;
        margin-left: -48px;
        overflow: hidden;
    }

    .aftersale-index .sa-table-wrap .sa-table .el-table__header-wrapper {
        margin-bottom: 4px;
    }

    .aftersale-index .sa-table-wrap .sa-table .el-table__row {
        background: var(--sa-background-hex-hover);
    }

    .aftersale-index .sa-table-wrap .sa-table .el-table__expanded-cell {
        padding: 0;
    }

    .aftersale-index .sa-table-wrap .sa-expand-table .el-table__header-wrapper {
        margin-bottom: 0;
        display: none;
    }

    .aftersale-index .sa-table-wrap .sa-expand-table .el-table__row {
        background: var(--el-table-tr-bg-color);
    }

    .aftersale-index .sa-expand-table tr:hover>td.el-table__cell {
        background-color: var(--el-table-tr-bg-color) !important;
    }

    .aftersale-index .order-detail {
        margin-left: 4px;
    }

    .aftersale-index .goods-item {
        line-height: 1;
    }

    .aftersale-index .goods-item .goods-image {
        margin-right: 12px;
    }

    .aftersale-index .goods-item .goods-title {
        height: 14px;
        line-height: 14px;
        font-size: 12px;
        font-weight: 600;
        color: var(--el-color-primary);
        cursor: pointer;
        margin-bottom: 6px;
    }

    .aftersale-index .goods-item .goods-price {
        flex-shrink: 0;
        line-height: 14px;
        font-size: 12px;
        color: var(--sa-font);
        margin-bottom: 12px;
    }

    .aftersale-index .goods-item .goods-num {
        margin-left: 8px;
    }

    .aftersale-index .goods-item .goods-sku-text {
        width: fit-content;
        height: 18px;
        line-height: 18px;
        padding: 0 8px;
        font-size: 12px;
        color: var(--sa-subfont);
        background: var(--sa-space);
        border-radius: 10px;
    }

    .aftersale-index .status--1 {
        color: var(--el-color-danger);
    }
</style>

<div id="index" class="aftersale-index panel panel-default panel-intro" v-cloak>
    <el-container class="panel-block">
        <el-header class="sa-header">
            <el-tabs class="sa-tabs" v-model="state.filter.data['aftersale_list.aftersale_status']"
                @tab-change="onChangeTab">
                <el-tab-pane v-for="item in type.data.aftersale_status" :key="item"
                    :label="`${item.name}${item.num ? '(' + item.num + ')' : ''}`" :name="item.type"></el-tab-pane>
            </el-tabs>
            <div class="sa-title sa-flex sa-row-between">
                <div class="sa-title-left">
                    <div class="left-name">售后管理</div>
                    <sa-filter-condition v-model="state.filter" @filter-delete="onChangeFilter">
                    </sa-filter-condition>
                </div>
                <div class="sa-title-right">
                    <el-button class="sa-button-refresh" icon="RefreshRight" @click="getData"></el-button>
                    <el-button class="sa-button-refresh" icon="Search" @click="onOpenFilter"></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="sa-main">
            <div class="sa-table-wrap">
                <el-table height="100%" class="sa-table" :data="state.data" stripe default-expand-all>
                    <el-table-column type="expand">
                        <template #default="props">
                            <el-table class="sa-table sa-expand-table" :data="props.row.aftersales" stripe>
                                <el-table-column width="48"></el-table-column>
                                <el-table-column min-width="280">
                                    <template #default="scope">
                                        <div class="aftersale-sn">
                                            售后单号：{{ scope.row.aftersale_sn }}
                                            <el-icon class="copy-document" @click="onClipboard(scope.row.aftersale_sn)">
                                                <copy-document />
                                            </el-icon>
                                        </div>
                                        <div class="create-time"> 申请时间：{{ scope.row.createtime }} </div>
                                    </template>
                                </el-table-column>
                                <el-table-column min-width="300">
                                    <template #default="scope">
                                        <div class="goods-item sa-flex sa-col-top">
                                            <sa-image class="mr-2" :url="scope.row.goods_image" size="64"></sa-image>
                                            <div>
                                                <div class="goods-title sa-table-line-1"
                                                    @click="onOpenGoods(scope.row.goods_id)">
                                                    {{scope.row.goods_title}}</div>
                                                <div class="goods-price">
                                                    ¥{{scope.row.goods_price}} <span
                                                        class="goods-num">x{{scope.row.goods_num}}</span>
                                                </div>
                                                <div v-if="scope.row.goods_sku_text" class="goods-sku-text">
                                                    {{scope.row.goods_sku_text}}
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column min-width="130">
                                    <template #default>{{ props.row.pay_fee }}</template>
                                </el-table-column>
                                <el-table-column prop="refund_fee" min-width="100"></el-table-column>
                                <el-table-column min-width="80">
                                    <template #default="scope">
                                        <div :class="scope.row.aftersale_status == '-1' ? 'status--1' : ''">
                                            {{ scope.row.aftersale_status_text }}
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="type_text" min-width="80"></el-table-column>
                                <el-table-column min-width="80">
                                    <template #default="scope">
                                        {if $auth->check('shopro/order/aftersale/detail')}
                                        <el-button type="primary" link @click="onDetail(scope.row.id)">详情</el-button>
                                        {/if}
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </el-table-column>
                    <el-table-column label="售后信息" min-width="280">
                        <template #default="scope">
                            <div class="order-sn sa-flex">
                                订单号：{{ scope.row.order_sn }}
                                <el-button class="order-detail" type="primary" link size="small"
                                    @click="onOpenOrderDetail(scope.row.id)">详情</el-button>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品信息" min-width="300">
                        <template #default="scope">
                            <div class="create-time"> 下单时间：{{ scope.row.createtime }} </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="订单实付金额" min-width="130">
                        <template #default="scope">
                            <sa-user-profile :user="scope.row.user" :id="scope.row.user_id" :isavatar="false">
                            </sa-user-profile>
                        </template>
                    </el-table-column>
                    <el-table-column label="已退款金额" min-width="100"></el-table-column>
                    <el-table-column label="处理状态" min-width="80"></el-table-column>
                    <el-table-column label="售后类型" min-width="80"></el-table-column>
                    <el-table-column label="操作" min-width="80"></el-table-column>
                </el-table>
            </div>
        </el-main>
        <el-footer class="sa-footer sa-flex sa-row-right">
            <sa-pagination v-model="pagination" @pagination-change="getData"></sa-pagination>
        </el-footer>
    </el-container>
    <sa-filter v-model="state.filter" @filter-change="onChangeFilter"></sa-filter>
</div>