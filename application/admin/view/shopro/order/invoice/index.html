{include file="/shopro/common/script" /}

<div id="index" class="invoice-index panel panel-default panel-intro" v-cloak>
    <el-container class="panel-block">
        <el-header class="sa-header">
            <el-tabs class="sa-tabs" v-model="state.filter.data.status" @tab-change="onChangeTab">
                <el-tab-pane v-for="item in type.data.status" :key="item"
                    :label="`${item.name}${item.num ? '(' + item.num + ')' : ''}`" :name="item.type"></el-tab-pane>
            </el-tabs>
            <div class="sa-title sa-flex sa-row-between">
                <div class="sa-title-left">
                    <div class="left-name">订单发票</div>
                    <sa-filter-condition v-model="state.filter" @filter-delete="onChangeFilter">
                    </sa-filter-condition>
                </div>
                <div class="sa-title-right">
                    <el-button class="sa-button-refresh" icon="RefreshRight" @click="getData"></el-button>
                    <el-button class="sa-button-refresh" icon="Search" @click="onOpenFilter"></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="sa-main">
            <el-table height="100%" class="sa-table" :data="state.data" stripe>
                <el-table-column prop="id" label="ID" min-width="90"> </el-table-column>
                <el-table-column label="申请用户" min-width="200">
                    <template #default="scope">
                        <sa-user-profile :user="scope.row.user" :id="scope.row.user_id"></sa-user-profile>
                    </template>
                </el-table-column>
                <el-table-column label="类型" min-width="110">
                    <template #default="scope">
                        <el-tag v-if="scope.row.type == 'person'">个人</el-tag>
                        <el-tag v-if="scope.row.type == 'company'" type="warning">企/事业单位</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="抬头名称" min-width="240">
                    <template #default="scope">
                        <div class="sa-table-line-1">
                            {{ scope.row.name || '-' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="联系电话" min-width="160">
                    <template #default="scope">
                        <div class="sa-table-line-1">
                            {{ scope.row.mobile || '-' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="总金额" min-width="130">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{scope.row.amount ? scope.row.amount + '元' : '-'}}</div>
                    </template>
                </el-table-column>
                <el-table-column label="实际开票金额" min-width="130">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{scope.row.invoice_amount ? scope.row.invoice_amount + '元' : '-'}}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="纳税人识别号" min-width="200">
                    <template #default="scope">
                        <div class="sa-table-line-1">
                            {{ scope.row.tax_no || '-' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="单位地址" min-width="300">
                    <template #default="scope">
                        <div class="sa-table-line-1">
                            {{ scope.row.address || '-' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="开户行" min-width="300">
                    <template #default="scope">
                        <div class="sa-table-line-1">
                            {{ scope.row.bank_name || '-' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="订单编号" min-width="250">
                    <template #default="scope">
                        <div class="sa-flex sa-table-line-1">
                            <el-button type="primary" link @click="onOpenOrderDetail(scope.row.order_id)">{{
                                scope.row.order?.order_sn || '-' }}</el-button>
                            <el-icon class="copy-document" @click="onClipboard(scope.row.order?.order_sn)">
                                <copy-document />
                            </el-icon>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="申请时间" min-width="180">
                    <template #default="scope">
                        {{ scope.row.createtime || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="处理时间" min-width="180">
                    <template #default="scope">
                        {{ scope.row.finish_time || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="状态" min-width="120">
                    <template #default="scope">
                        <div :class="`sa-color--${state.statusStyle[scope.row.status]}`">
                            {{ scope.row.status_text || '-' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" min-width="100">
                    <template #default="scope">
                        {if $auth->check('shopro/order/invoice/confirm')}
                        <el-button v-if="scope.row.status == 'waiting'" type="primary" link
                            @click="onConfirm(scope.row)">确认开具
                        </el-button>
                        {/if}
                    </template>
                </el-table-column>
            </el-table>
        </el-main>
        <el-footer class="sa-footer sa-flex sa-row-right">
            <sa-pagination v-model="pagination" @pagination-change="getData"></sa-pagination>
        </el-footer>
    </el-container>
    <sa-filter v-model="state.filter" @filter-change="onChangeFilter"></sa-filter>
</div>