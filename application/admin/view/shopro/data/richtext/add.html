{include file="/shopro/common/script" /}

<div id="addEdit" class="richtext-form" v-cloak>
    <el-container class="panel-block">
        <el-main>
            <el-scrollbar height="100%">
                <el-form :model="form.model" :rules="form.rules" ref="formRef" label-width="100px">
                    <el-form-item label="标题" prop="title">
                        <el-input class="sa-w-360" v-model="form.model.title" placeholder="请输入标题"></el-input>
                    </el-form-item>
                    <el-form-item label="内容" prop="content">
                        <form role="form">
                            <textarea id="richtextContent" class="editor"></textarea>
                        </form>
                    </el-form-item>
                </el-form>
            </el-scrollbar>
        </el-main>
        <el-footer class="sa-footer--submit sa-flex sa-row-right">
            <el-button type="primary" @click="onConfirm">确定</el-button>
        </el-footer>
    </el-container>
</div>