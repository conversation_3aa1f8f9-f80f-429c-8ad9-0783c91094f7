{include file="/shopro/common/script" /}

<div id="select" class="richtext-select" v-cloak>
    <el-container class="panel-block">
        <el-header>
            <div class="sa-title sa-flex sa-row-right">
                <el-button icon="Plus" type="primary" @click="onAdd">添加</el-button>
            </div>
        </el-header>
        <el-main class="sa-main">
            <el-table height="100%" class="sa-table" :data="state.data" stripe @selection-change="onChangeSelection"
                @sort-change="onChangeSort">
                <el-table-column type="selection" width="48" align="center"></el-table-column>
                <el-table-column prop="id" label="ID" min-width="90" sortable="custom"> </el-table-column>
                <el-table-column label="标题" min-width="120">
                    <template #default="scope">
                        <div class="sa-table-line-1">
                            {{ scope.row.title || '-' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" width="172">
                    <template #default="scope">
                        {{ scope.row.createtime || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="更新时间" width="172">
                    <template #default="scope">
                        {{ scope.row.updatetime || '-' }}
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" min-width="120">
                    <template #default="scope">
                        <el-button type="primary" link @click="onSelect(scope.row)">选择</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-main>
        <el-footer class="sa-footer sa-flex sa-row-right sa-flex-wrap">
            <sa-pagination class="is-ellipsis" v-model="pagination" @pagination-change="getData"></sa-pagination>
        </el-footer>
    </el-container>
</div>