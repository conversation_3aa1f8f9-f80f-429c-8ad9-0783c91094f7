{include file="/shopro/common/script" /}

<div id="addEdit" class="page-form" v-cloak>
  <el-container class="panel-block">
    <el-main>
      <el-scrollbar height="100%">
        <el-form :model="form.model" :rules="form.rules" ref="formRef" label-width="100px">
          <el-form-item label="名称" prop="name">
            <el-input class="sa-w-360" v-model="form.model.name" placeholder="请输入名称"></el-input>
          </el-form-item>
          <el-form-item label="路径" prop="path">
            <el-input class="sa-w-360" v-model="form.model.path" placeholder="请输入路径"></el-input>
          </el-form-item>
          <el-form-item label="分组" prop="group">
            <el-input class="sa-w-360" v-model="form.model.group" placeholder="请输入分组"></el-input>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-main>
    <el-footer class="sa-footer--submit sa-flex sa-row-right">
      <el-button type="primary" @click="onConfirm">确定</el-button>
    </el-footer>
  </el-container>
</div>