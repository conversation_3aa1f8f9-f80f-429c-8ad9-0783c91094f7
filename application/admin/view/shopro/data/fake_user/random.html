{include file="/shopro/common/script" /}

<div id="random" class="fake-user-random" v-cloak>
    <el-container class="panel-block">
        <el-main>
            <el-scrollbar height="100%">
                <el-form :model="form.model" :rules="form.rules" ref="formRef" label-width="100px">
                    <el-form-item label="生成人数" prop="num">
                        <el-input v-model="form.model.num" placeholder="请输入生成虚拟人数" type="number" min="0"></el-input>
                    </el-form-item>
                </el-form>
            </el-scrollbar>
        </el-main>
        <el-footer class="sa-footer--submit sa-flex sa-row-right">
            <el-button type="primary" @click="onConfirm">确定</el-button>
        </el-footer>
    </el-container>
</div>