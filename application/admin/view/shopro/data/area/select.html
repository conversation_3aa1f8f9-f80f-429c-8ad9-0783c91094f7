{include file="/shopro/common/script" /}

<div id="select" class="area-select" v-cloak>
    <el-container class="panel-block">
        <el-main>
            <el-scrollbar height="100%">
                <el-checkbox v-model="state.checkedAll" :indeterminate="isIndeterminate" label="全选" @change="onChange">
                </el-checkbox>
                <el-tree :data="state.data" node-key="id" show-checkbox :default-checked-keys="state.ids"
                    @check-change="onChangeCheck" ref="treeRef">
                    <template #default="{ data }">{{ data.name }}</template>
                </el-tree>
            </el-scrollbar>
        </el-main>
        <el-footer class="sa-footer--submit sa-flex sa-row-right">
            <el-button type="primary" @click="onConfirm">确定</el-button>
        </el-footer>
    </el-container>
</div>