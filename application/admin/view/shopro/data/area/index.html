{include file="/shopro/common/script" /}

<style>
    .area-index .sa-title .tip {
        margin-left: 8px;
        font-weight: 400;
        font-size: 12px;
        color: var(--sa-subfont);
    }

    .area-index .sa-title .tip a {
        color: var(--sa-subfont);
        text-decoration: underline;
    }

    .area-index .tree-header {
        height: 40px;
        padding-left: 20px;
        background: var(--sa-table-header-bg);
    }

    .area-index .tree-content .el-tree-node__content {
        height: 48px;
    }

    .area-index .tree-content .el-tree-node__label {
        flex: 1;
    }

    .area-index .tree-header .left,
    .area-index .tree-content .left {
        flex: 1;
    }

    .area-index .tree-header .id,
    .area-index .tree-content .id {
        margin-right: 12px;
        color: #999;
    }

    .area-index .tree-header .level,
    .area-index .tree-header .oper,
    .area-index .tree-content .level,
    .area-index .tree-content .oper {
        flex-shrink: 0;
        width: 120px;
    }
</style>

<div id="index" class="area-index panel panel-default panel-intro" v-cloak>
    <el-container class="panel-block">
        <el-header class="sa-header">
            <div class="sa-title sa-flex sa-flex-wrap sa-row-between">
                <div class="sa-title-left">
                    <div class="left-name">省市区</div>
                    <div class="tip">
                        数据来源：
                        <a href="http://www.stats.gov.cn/sj/tjbz/tjyqhdmhcxhfdm/2022/" target="_blank">
                            2022年国家统计局区划代码
                        </a>
                        更新时间2022-10-31
                    </div>
                </div>
                <div class="sa-title-right">
                    <el-button class="sa-button-refresh" icon="RefreshRight" @click="getData"></el-button>
                    {if $auth->check('shopro/data/area/add')}
                    <el-button icon="Plus" type="primary" @click="onAdd">添加</el-button>
                    {/if}
                </div>
            </div>
        </el-header>
        <el-main class="sa-main">
            <el-scrollbar height="100%">
                <div class="tree-header sa-flex">
                    <div class="left">名称</div>
                    <div class="level">级别</div>
                    <div class="oper">操作</div>
                </div>
                <el-tree class="tree-content" :data="state.data" node-key="id" ref="accessTree">
                    <template #default="{ node, data }">
                        <div class="tree-item sa-flex">
                            <div class="left sa-flex">
                                <div class="id"> #{{ data.id }} </div>
                                <div class="name">{{ data.name }}</div>
                            </div>
                            <div class="level">
                                {{ data.level == 'province' ? '省级' : data.level == 'city' ? '市级' : '区级' }}
                            </div>
                            <div class="oper">
                                {if $auth->check('shopro/data/area/edit')}
                                <el-button type="primary" link @click.stop="onEdit(node.data.id)">编辑</el-button>
                                {/if}
                                <el-popconfirm width="fit-content" confirm-button-text="确认" cancel-button-text="取消"
                                    title="确认删除这条记录?" @confirm="onDelete(node.data.id)">
                                    <template #reference>
                                        {if $auth->check('shopro/data/area/delete')}
                                        <el-button type="danger" link @click.stop>删除</el-button>
                                        {/if}
                                    </template>
                                </el-popconfirm>
                            </div>
                        </div>
                    </template>
                </el-tree>
            </el-scrollbar>
        </el-main>
    </el-container>
</div>