{include file="/shopro/common/script" /}

<div id="recharge" class="user-recharge" v-cloak>
    <el-container class="panel-block">
        <el-main>
            <el-form ref="formRef" :model="form.model" :rules="form.rules" label-width="100px">
                <el-form-item :label="`充值${state.type == 'money' ? '金额' : '积分'}`" prop="amount">
                    <el-input v-model="form.model.amount" type="number" placeholder="负数则为扣除" />
                </el-form-item>
                <el-form-item label="备注">
                    <el-input v-model="form.model.memo"></el-input>
                </el-form-item>
            </el-form>
        </el-main>
        <el-footer class="sa-footer--submit sa-flex sa-row-right">
            <el-button type="primary" @click="onConfirm">确定</el-button>
        </el-footer>
    </el-container>
</div>