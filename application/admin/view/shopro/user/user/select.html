{include file="/shopro/common/script" /}

<style>
    .user-select .search {
        cursor: pointer;
    }
</style>

<div id="select" class="user-select panel panel-default panel-intro" v-cloak>
    <el-container class="panel-block">
        <el-header class="sa-header pt-4 pb-0">
            <el-input v-model="state.filter.data.keyword" placeholder="请输入关键字" clearable>
                <template #append>
                    <el-icon class="search" @click="onChangeFilter">
                        <search></search>
                    </el-icon>
                </template>
            </el-input>
        </el-header>
        <el-main>
            <el-table height="100%" class="sa-table" :data="state.data" stripe>
                <el-table-column width="48" align="center">
                    <template #header="scope">
                        <el-checkbox :model-value="state.isSelectAll" :indeterminate="state.isIndeterminate"
                            @change="onSelectAll">
                        </el-checkbox>
                    </template>
                    <template #default="scope">
                        <el-checkbox :model-value="state.ids.includes(scope.row.id)"
                            @change="onSelect($event, scope.row)">
                        </el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column prop="id" label="ID" min-width="90" sortable="custom"> </el-table-column>
                <el-table-column label="用户信息" min-width="150">
                    <template #default="scope">
                        <sa-user-profile :user="scope.row" :id="scope.row.id"></sa-user-profile>
                    </template>
                </el-table-column>
                <el-table-column prop="username" label="用户名" min-width="120"></el-table-column>
                <el-table-column prop="mobile" label="手机号" min-width="120"></el-table-column>
                <el-table-column sortable="custom" prop="commission" label="佣金" min-width="110"></el-table-column>
                <el-table-column sortable="custom" prop="total_consume" label="总消费" min-width="110"></el-table-column>
                <el-table-column sortable="custom" prop="score" label="积分" min-width="110"></el-table-column>
                <el-table-column sortable="custom" prop="money" label="余额" min-width="110"></el-table-column>
            </el-table>
        </el-main>
        <el-footer class="sa-footer--submit sa-flex sa-row-between sa-flex-wrap">
            <sa-pagination class="is-ellipsis" v-model="pagination" @pagination-change="getData"></sa-pagination>
            <el-button type="primary" :disabled="state.ids.length==0" @click="onConfirm">确定</el-button>
        </el-footer>
    </el-container>
</div>