{include file="/shopro/common/script" /}

<div id="select" class="level-select" v-cloak>
    <el-container class="panel-block">
        <el-header class="sa-header">
            <el-alert class="mt-4">
                <template #title>温馨提示：更换等级后，该用户不会降级</template>
            </el-alert>
        </el-header>
        <el-main>
            <el-table height="100%" class="sa-table" :data="state.data" stripe>
                <el-table-column prop="name" label="等级信息" min-width="150"></el-table-column>
                <el-table-column label="等级权重" min-width="90">
                    <template #default="scope"> 等级{{ scope.row.level }} </template>
                </el-table-column>
                <el-table-column label="操作" min-width="80" fixed="right">
                    <template #default="scope">
                        <span v-if="scope.row.level == state.level" class="sa-color--info"> 已选择 </span>
                        <el-button v-else class="is-link" type="primary" @click="onSelect(scope.row.level)">选择
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-main>
        <el-footer class="sa-footer--submit sa-flex sa-row-right">
            <el-button type="primary" @click="onConfirm">确定</el-button>
        </el-footer>
    </el-container>
</div>