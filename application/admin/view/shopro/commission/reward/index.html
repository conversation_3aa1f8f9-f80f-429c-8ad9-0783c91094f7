{include file="/shopro/common/script" /}

<div id="index" class="reward-index panel panel-default panel-intro" v-cloak>
    <el-container class="panel-block">
        <el-header class="sa-header">
            <div class="sa-title sa-flex sa-row-between">
                <div class="sa-title-left">
                    <div class="left-name">佣金明细</div>
                    <sa-filter-condition v-model="state.filter" @filter-delete="onChangeFilter">
                    </sa-filter-condition>
                </div>
                <div class="sa-title-right">
                    <el-button class="sa-button-refresh" icon="RefreshRight" @click="getData"></el-button>
                    <el-button class="sa-button-refresh" icon="Search" @click="onOpenFilter"></el-button>
                    {if $auth->check('shopro/commission/reward/export')}
                    <el-button :loading="exportLoading" :disabled="exportLoading" @click="onExport('export')">导出
                    </el-button>
                    {/if}
                </div>
            </div>
        </el-header>
        <el-main class="sa-main">
            <el-table height="100%" class="sa-table" :data="state.data" stripe>
                <el-table-column prop="id" label="ID" min-width="90"></el-table-column>
                <el-table-column label="订单号" min-width="260">
                    <template #default="scope">
                        {{ scope.row.order?.order_sn || scope.row.order_id }}
                    </template>
                </el-table-column>
                <el-table-column label="下单用户" min-width="160">
                    <template #default="scope">
                        <sa-user-profile :user="scope.row.buyer" :id="scope.row.buyer_id"></sa-user-profile>
                    </template>
                </el-table-column>
                <el-table-column label="分销用户" min-width="160">
                    <template #default="scope">
                        <sa-user-profile :user="scope.row.agent" :id="scope.row.agent_id"></sa-user-profile>
                    </template>
                </el-table-column>
                <el-table-column label="分销金额" min-width="160">
                    <template #default="scope"> {{ scope.row.commission }}元 </template>
                </el-table-column>
                <el-table-column label="入账状态" min-width="80">
                    <template #default="scope">
                        <span :class="`sa-color--${state.statusStyle[scope.row.status]}`">
                            {{ scope.row.status_text }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="type_text" label="入账方式" min-width="80"></el-table-column>
                <el-table-column prop="commission_time" label="分佣时间" width="172"></el-table-column>
            </el-table>
        </el-main>
        <el-footer class="sa-footer sa-flex sa-row-right">
            <sa-pagination v-model="pagination" @pagination-change="getData"></sa-pagination>
        </el-footer>
    </el-container>
    <sa-filter v-model="state.filter" @filter-change="onChangeFilter"></sa-filter>
</div>