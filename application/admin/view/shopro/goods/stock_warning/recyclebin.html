{include file="/shopro/common/script" /}

<div id="recyclebin" class="stock-warning-recyclebin" v-cloak>
    <el-container class="panel-block">
        <el-main>
            <el-table height="100%" class="sa-table" :data="state.data" stripe @sort-change="onChangeSort">
                <el-table-column prop="id" label="ID" width="90" sortable="custom"></el-table-column>
                <el-table-column label="名称" min-width="120">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{ scope.row.goods?.title || scope.row.goods_id }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="deletetime" label="补货时间" width="172" sortable="custom"></el-table-column>
            </el-table>
        </el-main>
        <el-footer class="sa-footer sa-flex sa-row-right">
            <sa-pagination class="is-ellipsis" v-model="pagination" @pagination-change="getData"></sa-pagination>
        </el-footer>
    </el-container>
</div>