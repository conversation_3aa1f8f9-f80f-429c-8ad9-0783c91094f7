{include file="/shopro/common/script" /}

<div id="addEdit" class="service-form" v-cloak>
    <el-container class="panel-block">
        <el-main>
            <el-scrollbar height="100%">
                <el-form :model="form.model" :rules="form.rules" ref="formRef" label-width="100px">
                    <el-form-item label="名称" prop="name">
                        <el-input class="sa-w-360" v-model="form.model.name" placeholder="请输入名称"></el-input>
                    </el-form-item>
                    <el-form-item label="服务标识">
                        <sa-uploader v-model="form.model.image"></sa-uploader>
                    </el-form-item>
                    <el-form-item label="说明">
                        <el-input class="sa-w-360" v-model="form.model.description" placeholder="请输入说明"></el-input>
                    </el-form-item>
                </el-form>
            </el-scrollbar>
        </el-main>
        <el-footer class="sa-footer--submit sa-flex sa-row-right">
            <el-button type="primary" @click="onConfirm">确定</el-button>
        </el-footer>
    </el-container>
</div>