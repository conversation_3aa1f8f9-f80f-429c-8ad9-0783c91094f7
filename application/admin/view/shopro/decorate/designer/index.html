{include file="/shopro/common/script" /}

<style>
    .designer-index .template-item {
        width: 246px;
        height: 480px;
        border: 1px solid var(--sa-space);
        box-shadow: 0 0 4px #59595933;
        border-radius: 8px;
        margin-bottom: 20px;
        margin-right: 20px;
        position: relative;
        overflow: hidden;
    }

    .designer-index .template-item img {
        width: 100%;
    }

    .designer-index .template-item:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 16px rgba(89, 89, 89, 0.24);
    }

    .designer-index .template-item:hover .template-footer {
        opacity: 1;
    }

    .designer-index .template-footer {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: fit-content;
        background: var(--sa-background-assist);
        padding: 10px;
        transition: all 0.5s;
        opacity: 0;
    }

    .designer-index .template-footer .name {
        font-size: 16px;
        color: var(--sa-title);
        margin-bottom: 4px;
    }

    .designer-index .template-footer.platform {
        font-size: 14px;
        color: var(--sa-subtitle);
        margin-bottom: 4px;
    }

    .designer-index .template-footer.platform .iconfont {
        font-size: 20px;
    }

    .designer-index .template-footer .memo {
        font-size: 12px;
        color: var(--sa-subfont);
        margin-bottom: 4px;
    }

    .designer-index .template-footer .left {
        flex-shrink: 0;
    }
</style>

<div id="index" class="designer-index panel panel-default panel-intro" v-cloak>
    <el-container class="panel-block">
        <el-header class="sa-header">
            <div class="sa-title sa-flex sa-row-between">
                <div class="sa-title-left">
                    <div class="left-name">设计师模板</div>
                </div>
                <div class="sa-title-right">
                    <el-button class="sa-button-refresh" icon="RefreshRight" @click="getData"></el-button>
                </div>
            </div>
        </el-header>
        <el-main>
            <el-scrollbar>
                <div class="sa-flex sa-flex-wrap">
                    <div class="template-item" v-for="item in state.data">
                        <el-carousel trigger="click" height="480px" :autoplay="false" :loop="false"
                            indicator-position="none">
                            <el-carousel-item v-for="page in item.page" :key="page">
                                <img :src="page.image" />
                            </el-carousel-item>
                        </el-carousel>
                        <div class="template-footer">
                            <div class="name">{{ item.name }}</div>
                            <div class="platform sa-flex">
                                <div class="left">支持平台：</div>
                                <div v-if="item.platform">
                                    <i :class="`iconfont icon${pl} mr-1`" v-for="pl in item.platform" :style="{
                                            color: platformList.find((pf) => {
                                              return pf.type == pl;
                                            })?.color,
                                          }"></i>
                                </div>
                            </div>
                            <div class="memo sa-flex">
                                <div class="left">备注：</div>
                                <div>{{ item.memo }}</div>
                            </div>
                            <div class="oper sa-flex sa-row-right">
                                {if $auth->check('shopro/decorate/designer/use')}
                                <el-button type="primary" link size="small" @click="onUse(item.id)">
                                    使用
                                </el-button>
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>
            </el-scrollbar>
        </el-main>
    </el-container>
</div>