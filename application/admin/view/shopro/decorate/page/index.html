{include file="/shopro/common/script" /}

<script src="__CDN__/assets/addons/shopro/libs/html2canvas.min.js?v={$site.version|htmlentities}"></script>

<script
    src="__CDN__/assets/addons/shopro/components/decorate/d-color-picker.js?v={$site.version|htmlentities}"></script>
<script src="__CDN__/assets/addons/shopro/components/decorate/d-list.js?v={$site.version|htmlentities}"></script>
<script src="__CDN__/assets/addons/shopro/components/decorate/d-url.js?v={$site.version|htmlentities}"></script>
<script src="__CDN__/assets/addons/shopro/components/decorate/d-text-color.js?v={$site.version|htmlentities}"></script>
<script src="__CDN__/assets/addons/shopro/components/decorate/d-slider.js?v={$site.version|htmlentities}"></script>
<script
    src="__CDN__/assets/addons/shopro/components/decorate/d-goods-select.js?v={$site.version|htmlentities}"></script>
<script src="__CDN__/assets/addons/shopro/components/decorate/d-cube.js?v={$site.version|htmlentities}"></script>
<script src="__CDN__/assets/addons/shopro/components/decorate/center/header.js?v={$site.version|htmlentities}"></script>
<script src="__CDN__/assets/addons/shopro/components/decorate/center/navbar.js?v={$site.version|htmlentities}"></script>

<style>
    /* d-color-picker css */
    .d-color-picker {
        height: 32px;
    }

    .d-color-picker .empty {
        width: 32px;
        height: 32px;
        border-radius: 4px 0 0 4px;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }

    .d-color-picker .el-color-picker__trigger {
        display: flex;
        padding: 0;
        border-radius: 4px 0 0 4px;
        border-right: none;
        overflow: hidden;
    }

    .d-color-picker .el-color-picker__color {
        border: none;
    }

    .d-color-picker .el-color-picker__empty {
        display: none;
    }

    .d-color-picker .el-input {
        flex: 1;
    }

    .d-color-picker .el-input .el-input__wrapper {
        border-radius: 0 4px 4px 0;
    }

    .d-color-picker .el-checkbox {
        margin-left: 16px;
    }

    /* d-list css */
    .d-list .title {
        margin-bottom: 0;
    }

    .d-list .wrap {
        padding: 0 !important;
    }

    .d-list .list-item {
        padding: 8px 20px;
        border-bottom: 1px solid var(--sa-space);
        background: var(--sa-background-assist);
    }

    .d-list .list-item:last-of-type {
        border-bottom: none;
    }

    .d-list .list-item:hover {
        background: var(--t-bg-hover);
    }

    .d-list .list-item:hover .move>* {
        display: block;
    }

    .d-list .list-item .move {
        height: 32px;
        margin: 0 -8px 16px;
    }

    .d-list .list-item .move>* {
        display: none;
    }

    .d-list .list-item .move .sortable-drag {
        font-size: 16px;
        margin-right: 8px;
        cursor: pointer;
    }

    .d-list .list-item .move .list-delete {
        color: #ff4d4f;
        cursor: pointer;
    }

    /* d-text-color css */
    .d-text-color {
        width: 196px;
    }

    .d-text-color .el-input {
        flex: 1;
    }

    .d-text-color .el-input__wrapper {
        border-radius: 4px 0 0 4px;
    }

    .d-text-color .el-color-picker__trigger {
        display: flex;
        padding: 0;
        border-radius: 0 4px 4px 0;
        border-left: none;
        overflow: hidden;
    }

    .d-text-color .el-color-picker__color {
        border: none;
    }

    .d-text-color .el-color-picker__empty {
        display: none;
    }

    .d-text-color .color {
        position: relative;
    }

    .d-text-color .color .empty {
        width: 32px;
        height: 32px;
        border-radius: 0 4px 4px 0;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }

    /* d-slider css */
    .d-slider {
        width: 100%;
    }

    .d-slider .el-slider {
        --el-slider-height: 4px;
        --el-slider-button-size: 12px;
        flex: 1;
    }

    .d-slider .el-input {
        width: 70px;
        margin-left: 16px;
    }

    .d-slider input::-webkit-outer-spin-button,
    .d-slider input::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
    }

    .d-slider input[type='number'] {
        -moz-appearance: textfield;
    }

    /* .d-goods-select css */
    .d-goods-select .wrap {
        padding: 0 20px;
    }

    .d-goods-select .wrap .goods-item {
        margin: 0 8px 16px 0;
        position: relative;
        width: 44px;
        height: 44px;
    }

    .d-goods-select .wrap .goods-item:hover .goods-delete {
        display: flex;
    }

    .d-goods-select .wrap .goods-item .goods-delete {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        font-size: 16px;
        color: var(--sa-basic-mask-color);
        background: var(--sa-basic-mask-background);
        display: none;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .d-goods-select .wrap .add-button {
        margin: 0 0 16px !important;
    }

    .d-cube {
        position: relative;
        margin: 0 auto 16px;
        border-spacing: 0;
        border-collapse: collapse;
    }

    .d-cube .image-cube-item {
        border: 1px solid var(--sa-border);
        box-sizing: border-box;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .d-cube .image-cube-item .el-icon {
        font-size: 20px;
        color: var(--sa-place);
    }

    .d-cube .image-cube-item.is-active {
        background-color: var(--sa-background-hex-hover);
    }

    .d-cube .position-left {
        height: 100%;
        background: #fff;
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
    }

    .d-cube .position-left img {
        width: 38px;
    }

    .d-cube .WechatMiniProgram {
        position: absolute;
        top: -1px;
        right: -1px;
        width: 76px;
        height: 40px;
        background: #fff;
        display: flex;
        align-items: center;
    }

    .d-cube .WechatMiniProgram img {
        width: 100%;
        height: 30px;
    }

    .d-cube .position-item {
        position: absolute;
        background: var(--sa-background-hex-active);
        border: 1px solid var(--el-color-primary);
        cursor: pointer;
    }

    .d-cube .position-item .circle-close-filled {
        display: none;
        font-size: 12px;
        color: var(--el-color-primary);
        background: #fff;
        border-radius: 6px;
        position: absolute;
        top: -6px;
        right: -6px;
        z-index: 10;
    }

    .d-cube .position-item.is-active {
        background: var(--sa-background-hex-active);
        border: 1px solid var(--el-color-primary);
    }

    .d-cube .position-item.is-active .circle-close-filled {
        display: block;
    }

    .page-index .sa-header {
        --el-header-padding: 0;
        border-bottom: 1px solid var(--sa-border);
    }

    .page-index .sa-header .iconfont {
        font-size: 24px;
        color: var(--sa-subfont);
        cursor: pointer;
    }

    .page-index .sa-header .header-button {
        width: 48px;
        height: 40px;
        border-right: 1px solid var(--sa-border);
        cursor: pointer;
    }

    .page-index .sa-header .header-button.is-active {
        background: var(--el-color-primary);
    }

    .page-index .sa-header .header-button.is-active .iconfont {
        color: var(--sa-background-assist);
    }

    .page-index .sa-header .center {
        padding: 0 8px;
        border-left: 1px solid var(--sa-border);
        border-top: none;
        border-bottom: none;
    }

    .page-index .sa-header .header-icon {
        height: 100%;
        margin-right: 8px;
        cursor: pointer;
    }

    .page-index .sa-header .header-icon:nth-of-type(2),
    .page-index .sa-header .header-icon:nth-of-type(7),
    .page-index .sa-header .header-icon:last-of-type {
        margin-right: 0;
    }

    .page-index .sa-header .el-divider--vertical {
        height: 40px;
    }

    .page-index .sa-header .right .header-button {
        border-right: none;
        border-left: 1px solid var(--sa-border);
    }

    .page-index .sa-header .right .header-button:hover {
        background: var(--t-bg-active);
    }

    .page-index .page-main {
        --el-main-padding: 0;
        background: var(--sa-bg-assist-1);
    }

    .page-index .page-main .left-icon,
    .page-index .page-main .right-icon {
        width: 24px;
        height: 84px;
        color: var(--sa-font);
        background: var(--sa-space);
        border-radius: 0 24px 24px 0;
        border: 1px solid var(--sa-space);
        box-shadow: 0 0 4px rgb(0 0 0 / 8%), 0 2px 6px rgb(0 0 0 / 6%), 0 4px 8px 2px rgb(0 0 0 / 4%);
        position: absolute;
        top: 50%;
        z-index: 1;
        margin-top: -42px;
        cursor: pointer;
    }

    .page-index .page-main .left.is-collapse,
    .page-index .page-main .right.is-collapse {
        width: 0;
    }

    .page-index .page-main .left-main,
    .page-index .page-main .right-main {
        z-index: 2;
        background: var(--sa-background-assist);
    }

    .page-index .page-main .left {
        flex-shrink: 0;
        width: 260px;
        height: 100%;
        background: var(--sa-background-assist);
        box-shadow: 0px 0px 0.24rem rgb(0 0 0 / 16%);
        transition: all 0.2s;
        position: relative;
    }

    .page-index .page-main .left-main .el-collapse {
        --el-collapse-header-height: 32px;
        --el-collapse-header-bg-color: var(--sa-table-header-bg);
    }

    .page-index .page-main .left-main .el-collapse-item__wrap {
        border-bottom: none;
    }

    .page-index .page-main .left-main .el-collapse-item__header {
        padding: 0 12px;
    }

    .page-index .page-main .left-main .el-collapse-item__content {
        padding-bottom: 0;
    }

    .page-index .page-main .left-main .item {
        width: 86px;
        height: 86px;
        border-right: 1px solid var(--sa-space);
        border-bottom: 1px solid var(--sa-space);
        cursor: pointer;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .page-index .page-main .left-main .item:nth-of-type(3n) {
        border-right: none;
    }

    .page-index .page-main .left-main .item.is-active {
        background: var(--t-bg-active);
    }

    .page-index .page-main .left-main .item:not(.is-active):hover {
        background: var(--t-bg-hover);
    }

    .page-index .page-main .left-main img {
        width: 40px;
        height: 40px;
        margin-bottom: 8px;
    }

    .page-index .page-main .left-icon {
        right: -20px;
    }

    .page-index .page-main .center {
        height: 100%;
        flex: 1;
        padding-top: 10px;
        background: var(--sa-bg-assist-1);
        overflow-y: auto;
        position: relative;
        z-index: 0;
    }

    .page-index .page-main .center .center-main {
        position: absolute;
        top: 18px;
        width: 375px;
        min-height: calc(100% - 26px);
        background: var(--sa-background-assist);
        display: flex;
        flex-direction: column;
    }

    .page-index .page-main .center .center-main .ios-bar {
        width: 375px;
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: 10;
        pointer-events: none;
    }

    .page-index .page-main .center .center-main::before {
        content: "";
        position: absolute;
        top: -8px;
        left: 50%;
        right: 0;
        bottom: -8px;
        z-index: 1;
        margin-left: -195.5px;
        width: 375px;
        height: inherit;
        border: 8px solid var(--sa-title);
        box-sizing: content-box;
        pointer-events: none;
        cursor: move;
        overflow: hidden;
    }

    .page-index .page-main .center .center-main.is-android::before {
        border-radius: 16px;
    }

    .page-index .page-main .center .center-main.is-ios::before {
        border-radius: 40px;
    }

    .page-index .page-main .center .tabbar {
        height: 58px;
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
    }

    .page-index .page-main .center .tabbar .tabbar-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .page-index .page-main .center .tabbar .tabbar-item .text {
        height: 16px;
        line-height: 16px;
    }

    .page-index .page-main .center .tabbar .tabbar-item.is-center .top {
        width: 49px;
        height: 49px;
        border-radius: 50%;
        margin-bottom: 4px;
        background: linear-gradient(123deg, #fe8c00 0%, #ff6000 100%);
        box-shadow: 0 9px 13px 5px rgb(254 129 0 / 22%);
    }

    .page-index .page-main .center .tabbar .tabbar-item.is-center .text {
        display: none;
    }

    .page-index .page-main .center .mask {
        background: #eee;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }

    .page-index .page-main .center .float-menu {
        position: absolute;
        right: 30px;
        bottom: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .page-index .page-main .center .float-menu-item {
        margin-right: 10px;
        margin-bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .page-index .page-main .center .float-menu.vertical {
        flex-direction: column;
    }

    .page-index .page-main .center .float-menu.vertical .float-menu-item {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .page-index .page-main .center .float-menu-button {
        width: 42px;
        height: 42px;
        font-size: 20px;
        border-radius: 50%;
        color: #fff;
        transition: all 0.3s linear;
        cursor: pointer;
        transform: rotateZ(135deg);
        -webkit-transform: rotateZ(135deg);
    }

    .page-index .page-main .center .float-menu-button.fold {
        transform: rotateZ(0deg);
        -webkit-transform: rotateZ(0deg);
    }

    .page-index .page-main .center .popup-image-item {
        width: 292px;
        height: 454px;
        position: absolute;
        right: 50%;
        bottom: 50%;
        margin-right: -146px;
        margin-bottom: -227px;
        border: 1px solid var(--sa-border);
        background: #fff;
        border-radius: 4px;
    }

    .page-index .page-main .center .popup-image-item .sa-image {
        width: 292px;
        height: 454px;
    }

    .page-index .page-main .center .comp-wrap {
        position: relative;
        flex: 1;
        background-repeat: no-repeat !important;
        background-size: contain !important;
    }

    .page-index .page-main .center .comp-item {
        position: relative;
        z-index: 10;
        cursor: pointer;
    }

    .page-index .page-main .center .comp-item::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 1px;
        height: 100%;
        z-index: 1;
        pointer-events: none;
        cursor: move;
        display: none;
    }

    .page-index .page-main .center .comp-item:hover:before {
        display: flex;
        border: 1px dashed var(--el-color-primary);
    }

    .page-index .page-main .center .comp-item.is-active::before {
        display: flex;
        border: 1px solid var(--el-color-primary);
    }

    .page-index .page-main .center .comp-item .comp-label {
        position: absolute;
        top: 0;
        left: -88px;
        z-index: 1;
        width: 80px;
        height: 32px;
        background: var(--sa-background-assist);
        box-shadow: 0px 0px 4px rgb(0 0 0 / 8%), 0px 2px 6px rgb(0 0 0 / 6%), 0px 4px 8px 2px rgb(0 0 0 / 4%);
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 14px;
        color: var(--sa-font);
    }

    .page-index .page-main .center .comp-item.is-active .comp-label {
        color: #fff;
        background: var(--el-color-primary);
    }

    .page-index .page-main .center .comp-item .comp-tools {
        position: absolute;
        top: 0;
        right: -40px;
        z-index: 1;
        width: 32px;
        background: var(--sa-background-assist);
        box-shadow: 0px 0px 4px rgb(0 0 0 / 8%), 0px 2px 6px rgb(0 0 0 / 6%), 0px 4px 8px 2px rgb(0 0 0 / 4%);
        border-radius: 2px;
        padding-top: 8px;
        display: none;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .page-index .page-main .center .comp-item.is-active .comp-tools {
        display: flex;
    }

    .page-index .page-main .center .comp-item .comp-tools .el-icon {
        font-size: 16px;
        margin-bottom: 8px;
    }

    .page-index .page-main .center .comp-item .comp-content {
        background-size: 100% !important;
        background-repeat: no-repeat !important;
        overflow: hidden;
        min-height: 30px;
    }

    /* center 组件样式 */

    .page-index .page-main .right {
        flex-shrink: 0;
        width: 344px;
        height: 100%;
        background: var(--sa-background-assist);
        box-shadow: 0px 0px 0.24rem rgb(0 0 0 / 16%);
        transition: all 0.2s;
        position: relative;
        font-size: 12px;
    }

    .page-index .page-main .right * {
        font-size: inherit;
    }

    .page-index .page-main .right .el-form-item .el-form-item__content>* {
        width: 100%;
    }

    .page-index .page-main .right .right-icon {
        border-radius: 24px 0 0 24px;
        left: -20px;
    }

    .page-index .page-main .right .right-title {
        padding: 8px 12px;
        color: var(--sa-subtitle);
        font-size: 14px;
    }

    .page-index .page-main .right .right-title img {
        width: 24px;
        height: 24px;
        margin-right: 8px;
    }

    .page-index .page-main .right .right-tab {
        margin: 16px 20px;
        border-radius: 4px;
        border: 1px solid var(--sa-border);
        overflow: hidden;
    }

    .page-index .page-main .right .right-tab .tab-item {
        width: 50%;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-right: 1px solid var(--sa-border);
        background: var(--sa-border);
        /* border-radius: 4px 0 0 4px; */
        cursor: pointer;
    }

    .page-index .page-main .right .right-tab .tab-item:last-of-type {
        border-right: none;
        /* border-radius: 0 4px 4px 0; */
    }

    .page-index .page-main .right .right-tab .tab-item.is-active {
        background: var(--sa-background-assist);
    }

    .page-index .page-main .right .title {
        height: 40px;
        line-height: 40px;
        background: var(--sa-table-header-bg);
        margin-bottom: 16px;
        padding: 0 20px;
        font-size: 14px;
        color: var(--sa-subtitle);
        display: flex;
        align-items: center;
    }

    .page-index .page-main .right .title .tip {
        height: 14px;
        line-height: 14px;
        font-size: 12px;
        color: #999;
        margin-left: 4px;
    }

    .page-index .page-main .right .title .warning {
        line-height: 16px;
        font-size: 12px;
        color: var(--el-color-warning);
        margin-left: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .page-index .page-main .right .wrap {
        padding: 0 20px;
    }

    .page-index .page-main .right .add-button {
        width: fit-content;
        margin: 16px 20px;
        border-style: dashed;
    }

    .page-index .page-main .right .el-radio {
        margin-right: 24px;
    }

    .page-index .page-main .right .custom-radio-button .el-radio-button__inner {
        padding: 1px 6px;
    }

    .page-index .page-main .right .custom-radio-button .iconfont {
        font-size: 24px;
    }

    .page-index .page-main .right .notice-item {
        height: 28px;
        border: 1px solid var(--sa-border);
        border-radius: 4px;
        margin-right: 12px;
        cursor: pointer;
    }

    .page-index .page-main .right .notice-item.is-active {
        border-color: var(--el-color-primary);
    }

    .page-index .page-main .right .title-block-image {
        width: 100%;
        height: 32px;
        border: 1px solid var(--sa-border);
        border-radius: 4px;
        cursor: pointer;
    }

    .page-index .page-main .right .name {
        padding-left: 28px;
        line-height: 16px;
        font-size: 12px;
        font-weight: 400;
        color: var(--sa-title);
        margin-bottom: 4px;
    }

    .page-index .page-main .right .amount-text {
        padding-left: 28px;
        line-height: 16px;
        font-size: 12px;
        font-weight: 500;
        color: var(--sa-subfont);
        margin-bottom: 16px;
    }

    .title-block-dialog .item {
        height: 120px;
        border: 1px solid var(--sa-border);
        border-radius: 4px;
        position: relative;
    }

    .title-block-dialog .item.is-active {
        border-color: var(--el-color-primary);
    }

    .title-block-dialog .item img {
        width: 100%;
    }

    .title-block-dialog .item .item-title {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }

    /* userCard css */
    .user-card .item {
        padding: 24px 18px 10px;
    }

    .user-card img {
        width: 60px;
        height: 60px;
        margin-right: 16px;
    }

    .user-card .item-right .nickname {
        font-size: 18px;
        font-weight: bold;
        color: #000;
    }

    .user-card .item-right img {
        width: 20px;
        height: 20px;
    }

    .user-card .mobile {
        width: 100%;
        height: 42px;
        background: #fff;
        box-shadow: 0 8px 9px 0 rgba(224, 224, 224, 0.22);
        color: #ff690d;
        font-size: 12px;
        padding: 0 20px;
    }

    .user-card .mobile .mobile-button {
        width: 52px;
        height: 26px;
        line-height: 26px;
        text-align: center;
        background: #ff6100;
        border-radius: 26px;
        color: #fff;
        font-size: 12px;
    }

    .order-card img,
    .wallet-card img,
    .coupon-card img {
        width: 100%;
    }

    /* searchBlock css */
    .search-block {
        height: 32px;
        background: #fff;
        border: 1px solid #eee;
        padding: 0 12px;
        font-size: 14px;
        color: #b0b3bf;
    }

    .search-block .placeholder {
        color: #b0b3bf;
    }

    .search-block .keywords {
        height: 32px;
        font-size: 14px;
    }

    .search-block .keywords .keywords-item {
        padding: 5px 4px;
    }

    /* noticeBlock css */
    .notice-block {
        height: 24px;
    }

    .notice-block .sa-image {
        height: 100%;
    }

    .notice-block .el-divider--vertical {
        margin: 0;
        border-color: #eee;
    }

    .notice-block .text {
        min-height: 14px !important;
        height: 14px;
        line-height: 1;
        font-size: 14px;
        padding: 0 0 0 12px;
        overflow: hidden;
    }

    /* menuButton css */
    .menu-button {
        padding-bottom: 10px;
    }

    .menu-button .menu-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 16px;
        position: relative;
        margin: 15px 0 5px;
    }

    .menu-button .menu-item .title {
        height: 16px;
        line-height: 16px;
        margin-top: 8px;
        font-size: 12px;
    }

    .menu-button .menu-item .badge {
        position: absolute;
        top: 0;
        left: 50%;
        z-index: 1;
        padding: 0 6px;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        text-align: center;
        border-radius: 6px 6px 6px 0;
    }

    .menu-button .indicator {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .menu-button .indicator .iconfont {
        font-size: 30px;
        color: #333;
    }

    /* menuList css */
    .menu-list .menu-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 42px;
        padding: 0 12px;
        border-bottom: 1px solid #eee;
    }

    .menu-list .menu-item:last-of-type {
        border-bottom: none;
    }

    .menu-list .menu-item .sa-image {
        margin-right: 8px;
    }

    .menu-list .menu-item .title {
        font-size: 16px;
    }

    .menu-list .menu-item .tip {
        font-size: 12px;
    }

    .menu-list .menu-item .el-icon {
        font-size: var(--sa-subfont);
    }

    /* menuGrid css */
    .menu-grid .menu-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 13px;
        position: relative;
        padding: 20px 0 14px;
    }

    .menu-grid .menu-item .title {
        height: 16px;
        line-height: 16px;
        margin-top: 8px;
        font-size: 12px;
    }

    .menu-grid .menu-item .tip {
        height: 12px;
        line-height: 12px;
        margin-top: 4px;
        font-size: 10px;
        color: #faad14;
    }

    .menu-grid .menu-item .badge {
        position: absolute;
        top: 20px;
        left: 50%;
        z-index: 1;
        padding: 0 6px;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        text-align: center;
        border-radius: 6px 6px 6px 0;
    }

    /* goodsCard css */
    .goods-card .desc {
        padding: 10px;
        flex: 1;
        font-size: 12px;
    }

    .goods-card .title {
        height: 14px;
        line-height: 14px;
        font-size: 14px;
    }

    .goods-card .subtitle {
        height: 14px;
        line-height: 14px;
        font-size: 12px;
    }

    .goods-card .promos {
        height: fit-content;
    }

    .goods-card .promo-tag {
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        border: 1px solid #f00;
        padding: 0 4px;
        font-size: 12px;
        color: #f00;
        margin-right: 4px;
    }

    .goods-card .promo-tag:last-of-type {
        margin-right: 0;
    }

    .goods-card .goods-card-1 .sa-image {
        width: 100%;
        height: 180px;
    }

    .goods-card .goods-card-2 .sa-image {
        width: 100%;
        min-height: 180px;
    }

    .goods-card .goods-card-3 .item-wrap {
        display: flex;
    }

    .goods-card .goods-card-3 .sa-image {
        width: 130px;
        height: 130px;
    }

    .goods-card .goods-card-3 .price {
        display: inline-block;
        margin-right: 8px;
    }

    /* goodsSheleves css */
    .goods-shelves .desc {
        padding: 10px;
        flex: 1;
    }

    .goods-shelves .name {
        font-size: 13px;
    }

    .goods-shelves .price {
        font-size: 12px;
    }

    .goods-shelves .goods-shelves-1 .item-wrap {
        display: flex;
        height: 64px;
    }

    .goods-shelves .goods-shelves-1 .sa-image {
        width: 64px;
        height: 64px;
    }

    .goods-shelves .goods-shelves-2 .sa-image {
        width: 100%;
        height: 110px;
    }

    .goods-shelves .goods-shelves-3 .sa-image {
        width: 100%;
        height: 110px;
    }

    /* imageBlock css */
    .image-block {
        /* overflow: hidden; */
    }

    .image-block .sa-image {
        width: 100%;
        height: 100%;
    }

    /* imageBanner */
    .image-banner {
        height: fit-content !important;
    }

    .image-banner .banner-item {
        position: relative;
        overflow: hidden;
    }

    .image-banner .sa-image {
        width: 100%;
        height: 100%;
    }

    .image-banner .indicator {
        position: absolute;
        bottom: 0;
        left: 50%;
        margin-left: -15px;
    }

    .image-banner .indicator .iconfont {
        color: #fff;
    }

    .image-banner .banner-right {
        width: 375px;
        height: 100px;
        position: absolute;
        top: 50%;
        right: -364px;
    }

    /* titleBlock css */
    .title-block {
        position: relative;
        height: 100%;
    }

    .title-block .title {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 8;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .title-block .sa-image {
        width: 100%;
        height: 100%;
    }

    .title-block .more {
        position: absolute;
        right: 10px;
        top: 0;
        line-height: 40px;
        font-size: 12px;
        color: #999;
    }

    /* imageCube css */
    .image-cube .image-cube-item {
        position: absolute;
    }

    /* videoPlayer css */
    .video-player {
        height: 200px;
        background: #f3f3f3;
    }

    .video-player .sa-image,
    .video-player video {
        width: 100%;
        height: 100%;
    }

    /* lineBlock css */
    .line-block {
        height: 30px;
    }

    .line-block .line {
        width: 100%;
        border-bottom-width: 1px;
    }

    /* groupon css */
    .groupon .desc {
        padding: 10px;
        flex: 1;
        font-size: 12px;
    }

    .groupon .title {
        font-size: 14px;
    }

    .groupon .price {
        font-size: 14px;
    }

    .groupon .stock {
        color: #c4c4c4;
    }

    .groupon .groupon-1 .sa-image {
        width: 100%;
        height: 110px;
    }

    .groupon .groupon-2 .item-wrap {
        display: flex;
        height: 130px;
    }

    .groupon .groupon-2 .sa-image {
        width: 130px;
        height: 100%;
    }

    .groupon .groupon-2 .desc {
        width: calc(100% - 130px);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    /* seckill css */
    .seckill .desc {
        padding: 10px;
        flex: 1;
        font-size: 12px;
    }

    .seckill .title {
        font-size: 14px;
    }

    .seckill .price {
        font-size: 14px;
    }

    .seckill .stock {
        color: #c4c4c4;
    }

    .seckill .seckill-1 .sa-image {
        width: 100%;
        height: 110px;
    }

    .seckill .seckill-2 .item-wrap {
        display: flex;
        height: 130px;
    }

    .seckill .seckill-2 .sa-image {
        width: 130px;
        height: 100%;
    }

    .seckill .seckill-2 .desc {
        width: calc(100% - 130px);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    /* scoreGoods css */
    .score-goods .desc {
        padding: 10px;
        flex: 1;
        font-size: 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .score-goods .title {
        font-size: 14px;
    }

    .score-goods .sales-stock {
        color: #c4c4c4;
    }

    .score-goods .score-icon {
        width: 18px;
        height: 18px;
        margin-right: 4px;
    }

    .score-goods .score-goods-1 .sa-image {
        width: 100%;
        height: 140px;
    }

    .score-goods .score-goods-2 .item-wrap {
        display: flex;
        height: 140px;
    }

    .score-goods .score-goods-2 .sa-image {
        width: 140px;
        height: 140px;
    }

    .score-goods .score-goods-2 .desc {
        width: calc(100% - 140px);
    }

    /* mplive css */
    .mplive .item-wrap {
        position: relative;
    }

    .mplive .status {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 2;
        width: 72px;
        height: 20px;
        background: rgba(38, 38, 38, 0.48);
        border-radius: 10px;
    }

    .mplive .status .icon {
        width: 22px;
        height: 20px;
    }

    .mplive .status .tip {
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        color: #FDFDFD;
        margin-left: 6px;
    }

    .mplive .sa-image {
        width: 100%;
        height: 200px;
    }

    .mplive .bottom{
        width: 100%;
        height: 64px;
        background: linear-gradient(360deg, rgba(38, 38, 38, 0.8) 0%, rgba(38, 38, 38, 0.02) 100%);
        position: absolute;
        right: 0;
        bottom: 0;
    }

    .mplive .name {
        position: absolute;
        left: 10px;
        bottom: 28px;
        z-index: 2;
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        color: #FDFDFD;
    }

    .mplive .anchor_name {
        position: absolute;
        left: 10px;
        bottom: 8px;
        z-index: 2;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        color: #FDFDFD;
    }

    /* coupon css */
    .coupon .item-wrap {
        box-shadow: none !important;
    }

    .coupon .coupon-1.card-wrap {
        flex-wrap: nowrap;
    }

    .coupon .coupon-1 .item-wrap {
        width: 100%;
        height: 78px;
        padding: 0 10px 0 20px;
        flex-shrink: 0;
        line-height: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .coupon .coupon-1 .amount {
        font-size: 24px;
    }

    .coupon .coupon-1 .amount-text {
        font-size: 12px;
        font-weight: 500;
        margin-top: 6px;
    }

    .coupon .coupon-1 .time {
        font-size: 12px;
        margin-top: 4px;
    }

    .coupon .coupon-1 .coupon-button {
        height: 24px;
        line-height: 24px;
        padding: 0 10px;
        text-align: center;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .coupon .coupon-1 .stock {
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        margin-top: 12px;
    }

    .coupon .coupon-2 .item-wrap {
        width: 100%;
        height: 84px;
        padding: 0 10px 0 20px;
        line-height: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .coupon .coupon-2 .amount {
        font-size: 18px;
    }

    .coupon .coupon-2 .amount-text {
        font-size: 12px;
        font-weight: 500;
        margin-top: 6px;
    }

    .coupon .coupon-2 .stock {
        font-size: 12px;
        font-weight: 500;
        margin-top: 6px;
    }

    .coupon .coupon-2 .coupon-button {
        width: 20px;
        height: fit-content;
        padding: 4px 0;
        text-align: center;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 500;
        display: flex;
        flex-direction: column;
    }

    .coupon .coupon-3 .item-wrap {
        width: 100%;
        height: 146px;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .coupon .coupon-3 .amount {
        font-size: 25px;
        font-weight: 600;
        margin-top: 24px
    }

    .coupon .amount span {
        font-size: 12px;
        font-weight: 500;
        margin-left: 2px;
    }

    .coupon .coupon-3 .amount-text {
        width: 80px;
        height: 36px;
        line-height: 18px;
        text-align: center;
        font-size: 12px;
        font-weight: 500;
        margin-top: 10px;
    }

    .coupon .coupon-3 .coupon-button {
        height: 24px;
        line-height: 24px;
        padding: 0 10px;
        text-align: center;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        margin-top: 5px;
        /* margin-top: 20px; */
    }

    /* navbar css */
    .center-header {
        background-repeat: no-repeat !important;
        background-size: 100% 100% !important;
        pointer-events: none;
    }

    .center-header .system {
        width: 100%;
    }

    .center-header .center-navbar {
        padding: 9px 6px;
        width: 100%;
    }

    .center-header .center-navbar .header-diypage {
        width: 100%;
    }

    .center-navbar .navbar-wrap {
        width: inherit;
        height: 32px;
        position: relative;
    }

    .center-navbar .navbar-wrap .item {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .center-navbar .navbar-wrap .text {
        height: 32px;
        line-height: 32px;
        font-size: 16px;
    }

    .center-navbar .navbar-wrap .search {
        width: 100%;
        height: 32px;
        line-height: 32px;
        border: 1px solid #eee;
        padding: 0 10px;
        font-size: 12px;
        color: var(--sa-font);
        background: #fff;
    }

    .center-header .WechatMiniProgram-wrap {
        height: 50px;
        padding: 0 6px 0 0;
    }

    .center-header .WechatMiniProgram-wrap .WechatMiniProgram {
        height: 30px;
    }

    .WechatOfficialAccount {
        width: 100%;
        display: block;
    }

    .card-wrap {
        display: flex;
        flex-wrap: wrap;
    }

    .card-wrap .item-wrap {
        line-height: 1;
        background: #fff;
        /* box-shadow: 0 5px 21px #eaeaea75; */
        border-radius: 5px;
        overflow: hidden;
        position: relative;
    }

    .card-wrap .item-wrap .tag {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
    }

    .card-wrap .item-wrap .tag .sa-image {
        width: 36px !important;
        height: 22px !important;
    }

    .card-wrap .item-wrap .button {
        position: absolute;
        right: 10px;
        bottom: 10px;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        color: #fff;
        padding: 0 12px;
        border-radius: 12.5px;
        background-size: 100% 100% !important;
    }

    .el-dialog.hotzone-dialog {
        --el-dialog-width: 800px;
    }

    .hotzone-dialog .mapContent {
        width: 750px;
        position: relative;

    }

    .hotzone-dialog .mapContent img {
        width: 750px !important;
        pointer-events: none;
    }

    .hotzone-dialog .map-item {
        position: absolute;
        border: 1px solid var(--el-color-primary);
        cursor: move;
        background: var(--t-bg-active);
        opacity: 0.8;
        color: var(--el-color-primary);
        font-size: 12px;
    }

    .hotzone-dialog .map-item .delete {
        display: none;
    }

    .hotzone-dialog .map-item .coor {
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 11;
        width: 10px;
        height: 10px;
        background: var(--el-color-primary);
        cursor: se-resize;
    }

    .hotzone-dialog .map-item:hover .delete {
        display: block;
        position: absolute;
        top: -1px;
        right: -1px;
        width: 16px;
        height: 16px;
        background: var(--el-color-primary);
        border-radius: 0 0 0 100%;
        cursor: pointer;
        font-size: 12px;
        color: #ffffff;
        text-align: right;
    }

    .hotzone {
        /* overflow: hidden; */
        position: reactive;

    }

    .hotzone .sa-image {
        width: 100%;
        height: unset;
        min-height: 30px;
    }

    .hotzone .map-item {
        position: absolute;
        border: 1px solid var(--el-color-primary);
        background: var(--t-bg-active);
        opacity: 0.8;
        color: var(--el-color-primary);
        font-size: 12px;
    }

    .cssCard {
        background-color: #1e1e1e;
        color: #ccc;
        margin: 0 20px;
        padding: 10px;
        word-break: break-all;
    }

    .cssKey {
        color: #9cdcfe;
        flex-shrink: 0;
    }

    .cssValue {
        color: #ccc;
    }
</style>

<div id="index" class="page-index  panel panel-default panel-intro" v-cloak>
    <el-container class="panel-block">
        <el-header class="sa-header sa-flex sa-row-between">
            <div class="left sa-flex">
                <template v-if="state.pageType!='diypage'">
                    <el-popover popper-class="sa-popper" trigger="hover" :content="item.label"
                        v-for="item in pageTypeList">
                        <template #reference>
                            <div class="header-button sa-flex sa-row-center"
                                :class="state.pageType==item.type?'is-active':''" @click="onChangePageType(item.type)">
                                <i :class="`iconfont icon${item.type}`"></i>
                            </div>
                        </template>
                    </el-popover>
                </template>
            </div>
            <div class="center sa-flex">
                <el-popover popper-class="sa-popper" trigger="hover" :content="item.label" v-for="item in systemList">
                    <template #reference>
                        <div class="header-icon sa-flex sa-row-center" @click="state.systemType = item.type">
                            <i :class="`iconfont icon${item.type}`"
                                :style="{ color: state.systemType == item.type ? item.color : '' }"></i>
                        </div>
                    </template>
                </el-popover>
                <el-divider direction="vertical"></el-divider>
                <el-popover popper-class="sa-popper" trigger="hover" :content="item.label" v-for="item in platformList">
                    <template #reference>
                        <div class="header-icon sa-flex sa-row-center" @click="state.platformType = item.type">
                            <i :class="`iconfont icon${item.type}`"
                                :style="{ color: state.platformType == item.type ? item.color : '' }"></i>
                        </div>
                    </template>
                </el-popover>
                <el-divider direction="vertical"></el-divider>
                <el-popover popper-class="sa-popper" trigger="hover" content="页面设置">
                    <template #reference>
                        <div>
                            <i v-if="state.pageType != 'basic'" class="iconfont iconpage-setting"
                                :style="{ color: currentComp.index == -1 ? 'var(--el-color-primary)' : '' }"
                                @click="onUpdatePageSetting"></i>
                        </div>
                    </template>
                </el-popover>
            </div>
            <div class="right sa-flex">
                <el-popover popper-class="sa-popper" trigger="hover" content="预览">
                    <template #reference>
                        {if $auth->check('shopro/decorate/page/preview')}
                        <div class="header-button sa-flex sa-row-center" @click="onPreview">
                            <i class="iconfont iconpreview"></i>
                        </div>
                        {/if}
                    </template>
                </el-popover>
                <el-popover popper-class="sa-popper" trigger="hover" content="保存">
                    <template #reference>
                        {if $auth->check('shopro/decorate/page/edit')}
                        <div class="header-button sa-flex sa-row-center" @click="onSave">
                            <i class="iconfont iconsave"></i>
                        </div>
                        {/if}
                    </template>
                </el-popover>
            </div>
        </el-header>
        <el-main class="page-main sa-flex">
            <!-- 左侧组件 -->
            <div class="left" :class="!leftData.collapsePanel ? 'is-collapse' : ''">
                <el-scrollbar height="100%" class="left-main">
                    <el-collapse v-if="state.pageType=='basic'" v-model="leftData.activeCollapse">
                        <el-collapse-item v-for="item in pageLeft.basic" :title="item.name" :name="item.type">
                            <div class="sa-flex sa-flex-wrap">
                                <template v-for="element in item.data" :key="element">
                                    <div class="item" :class="[
                                      element.type,
                                      item.type == 'basic' && leftData.activeBasic == element.type ? 'is-active' : '',
                                      item.type == 'theme' && leftData.activeTheme == element.type ? 'is-active' : '',
                                    ]" @click.stop="onSelectLeftBasic(item.type, element.type)">
                                        <img :src="`/assets/addons/shopro/img/decorate/${element.type}.png`" />
                                        <span>{{ element.name }}</span>
                                    </div>
                                </template>
                            </div>
                        </el-collapse-item>
                    </el-collapse>
                    <el-collapse v-if="state.pageType!='basic'" v-model="leftData.activeCollapse">
                        <template v-for="item in pageLeft.compList">
                            <el-collapse-item v-if="!item.show || (item.show && item.show.includes(state.pageType))"
                                :title="item.name" :name="item.type">
                                <draggable class="sa-flex sa-flex-wrap" v-model="item.data" item-key="element" :group="{
              name: 'draggableName',
              pull: 'clone',
              put: false,
            }" ghostClass="sortable-ghost" fallbackClass="clone-item" :fallbackOnBody="true" :move="onLeftMove"
                                    @start="onLeftStart" @end="onLeftEnd">
                                    <template #item="{ element }">
                                        <div class="item" :class="element.type"
                                            @click.stop="onSelectLeftComp(element.type)">
                                            <img :src="`/assets/addons/shopro/img/decorate/${element.type}.png`" />
                                            <span>{{ element.name }}</span>
                                        </div>
                                    </template>
                                </draggable>
                            </el-collapse-item>
                        </template>
                    </el-collapse>
                </el-scrollbar>
                <div class="left-icon sa-flex sa-row-center" @click="leftData.collapsePanel = !leftData.collapsePanel">
                    <el-icon v-if="leftData.collapsePanel">
                        <arrow-left />
                    </el-icon>
                    <el-icon v-else>
                        <arrow-right />
                    </el-icon>
                </div>
            </div>
            <!-- 中间显示 -->
            <div class="center sa-flex sa-row-center" @click.self="onUpdatePageSetting">
                <div id="html2canvasWrap" class="center-main" :class="`${state.pageType} is-${state.systemType}`">
                    <template v-if="state.pageType=='basic'">
                        <div class="comp-wrap">
                            <div v-if="currentComp.type=='tabbar'" class="tabbar sa-flex" :style="{
                                background:
                                  centerData.templateData[state.pageType][currentComp.type].background.type == 'color'
                                    ? centerData.templateData[state.pageType][currentComp.type].background.bgColor
                                    : centerData.templateData[state.pageType][currentComp.type].background.bgImage
                                    ? 'url(' + Fast.api.cdnurl(centerData.templateData[state.pageType][currentComp.type].background.bgImage) + ')'
                                    : centerData.templateData[state.pageType][currentComp.type].background.bgImage,
                                'background-size': '100% 58px',
                                'background-repeat': 'no-repeat',
                              }">
                                <div class="tabbar-item" :class="
                                        centerData.templateData[state.pageType][currentComp.type].list.length % 2 == 1 &&
                                        centerData.templateData[state.pageType][currentComp.type].mode == 2 &&
                                        index == ((centerData.templateData[state.pageType][currentComp.type].list.length % 2) + centerData.templateData[state.pageType][currentComp.type].list.length) / 2 - 1
                                            ? 'is-center'
                                            : ''
                                        "
                                    v-for="(item, index) in centerData.templateData[state.pageType][currentComp.type].list"
                                    :key="item" :style="{
                                        width: 100 / centerData.templateData[state.pageType][currentComp.type].list.length + '%',
                                        }">
                                    <div class="top sa-flex sa-row-center">
                                        <sa-image :url="index == 0 ? item.activeIcon : item.inactiveIcon" size="26"
                                            radius="0" :ispreview="false">
                                        </sa-image>
                                    </div>
                                    <div class="text" :style="{
                                                color: index == 0 ? centerData.templateData[state.pageType][currentComp.type].activeColor : centerData.templateData[state.pageType][currentComp.type].inactiveColor,
                                            }">
                                        {{ item.text }}
                                    </div>
                                </div>
                            </div>
                            <div v-if="currentComp.type=='floatMenu' && centerData.templateData[state.pageType][currentComp.type].show"
                                :class="!centerData.floatMenuIsFold ? 'mask' : ''">
                                <div class="float-menu"
                                    :class="centerData.templateData[state.pageType][currentComp.type].mode == 2 ? 'horizontal' : 'vertical'">
                                    <template v-if="!centerData.floatMenuIsFold">
                                        <div class="float-menu-item"
                                            v-for="item in centerData.templateData[state.pageType][currentComp.type].list"
                                            :key="item">
                                            <sa-image :url="item.src" size="26" radius="0" :ispreview="false">
                                            </sa-image>
                                            <div v-if="centerData.templateData[state.pageType][currentComp.type].isText"
                                                class="text" :style="{color: item.title.color}">
                                                {{ item.title.text }}
                                            </div>
                                        </div>
                                    </template>
                                    <div class="float-menu-button sa-flex sa-row-center"
                                        :class="!centerData.floatMenuIsFold ? 'fold' : ''" :style="buttonStyle"
                                        @click="centerData.floatMenuIsFold = !centerData.floatMenuIsFold">
                                        <el-icon>
                                            <close-bold />
                                        </el-icon>
                                    </div>
                                </div>
                            </div>
                            <div v-if="currentComp.type=='popupImage'" class="popup-image">
                                <div class="mask"></div>
                                <div class="popup-image-item"
                                    v-for="(item, index) in centerData.templateData[state.pageType][currentComp.type].list"
                                    :key="item" :style="{
                                                'margin-right': `-${146 + index * 20}px`,
                                                'margin-bottom': `-${227 + index * 20}px`,
                                                'z-index': centerData.popupImageCurrent == index ? 200 : index,
                                              }" @click="centerData.popupImageCurrent = index">
                                    <sa-image :url="item.src" radius="0" :ispreview="false"></sa-image>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template v-if="state.pageType != 'basic' && centerData.templateData[state.pageType]">
                        <center-header :header="centerData.templateData[state.pageType].style" :page="state.pageType"
                            :system="state.systemType" :platform="state.platformType"></center-header>
                        <draggable class="comp-wrap" :style="pageStyle()"
                            v-model="centerData.templateData[state.pageType].data" item-key="element" :animation="300"
                            :group="{name: 'draggableName'}" filter=".undraggable" :move="onCenterMove"
                            @end="onCenterEnd">
                            <template #item="{ element, index }">
                                <div class="comp-item"
                                    :class="[element.type, currentComp.index == index ? 'is-active' : '']"
                                    @click.stop="onSelectComp(index, element.type)">
                                    <div v-if="!element.name" class="comp-content"
                                        :class="element.type == 'userCard' ? 'undraggable' : ''"
                                        :style="compStyle(element)">

                                        <div v-if="element.type=='userCard'" class="user-card">
                                            <div class="item sa-flex">
                                                <img src="/assets/addons/shopro/img/decorate/avatar.png" />
                                                <div class="item-right sa-flex sa-row-between sa-flex-1">
                                                    <div class="nickname">杨柳依依</div>
                                                    <img src="/assets/addons/shopro/img/decorate/qrcode.png" />
                                                </div>
                                            </div>
                                            <div class="mobile sa-flex sa-row-between">
                                                <div>点击绑定手机号</div>
                                                <div class="mobile-button">去绑定</div>
                                            </div>
                                        </div>

                                        <div v-if="element.type=='orderCard'" class="order-card">
                                            <img src="/assets/addons/shopro/img/decorate/orderCardStyle.png" />
                                        </div>

                                        <div v-if="element.type=='walletCard'" class="wallet-card">
                                            <img src="/assets/addons/shopro/img/decorate/walletCardStyle.png" />
                                        </div>

                                        <div v-if="element.type=='couponCard'" class="coupon-card">
                                            <img src="/assets/addons/shopro/img/decorate/couponCardStyle.png" />
                                        </div>

                                        <div v-if="element.type=='searchBlock'"
                                            class="search-block sa-flex sa-row-between"
                                            :style="{ 'border-radius': `${element.data.borderRadius}px` }">
                                            <div class="sa-flex">
                                                <el-icon>
                                                    <search />
                                                </el-icon>
                                                <div class="placeholder sa-m-l-8">{{ element.data.placeholder }}</div>
                                            </div>
                                            <div class="keywords sa-flex">
                                                <div class="keywords-item" v-for="k in element.data.keywords" :key="k"
                                                    :style="{ color: k.color }">
                                                    {{ k.text }}
                                                </div>
                                            </div>
                                        </div>

                                        <div v-if="element.type=='noticeBlock'" class="notice-block sa-flex">
                                            <sa-image :url="element.data.src" radius="0" :ispreview="false"></sa-image>
                                            <el-divider direction="vertical"></el-divider>
                                            <div class="text" :style="{ color: element.data.title.color }">
                                                {{ element.data.title.text }}
                                            </div>
                                        </div>

                                        <div v-if="element.type=='menuButton'" class="menu-button sa-flex sa-flex-wrap">
                                            <template v-for="(l, lindex) in element.data.list" :key="l">
                                                <div v-if="lindex < element.data.row * element.data.col"
                                                    class="menu-item" :style="{width: 100 / element.data.col + '%'}">
                                                    <div v-if="l.badge.show" class="badge"
                                                        :style="{ background: l.badge.bgColor, color: l.badge.color }">
                                                        {{ l.badge.text }}
                                                    </div>
                                                    <sa-image :url="l.src" size="49" radius="0" :ispreview="false">
                                                    </sa-image>
                                                    <div v-if="element.data.layout == 1" class="title"
                                                        :style="{ color: l.title.color }">
                                                        {{ l.title.text }}
                                                    </div>
                                                </div>
                                            </template>
                                            <div v-if="element.data.list.length > element.data.row * element.data.col"
                                                class="indicator">
                                                <i class="iconfont icondot-1"></i>
                                            </div>
                                        </div>

                                        <div v-if="element.type=='menuList'" class="menu-list">
                                            <template v-for="l in element.data.list" :key="l">
                                                <div class="menu-item">
                                                    <div class="sa-flex">
                                                        <sa-image v-if="l.src" :url="l.src" size="16" radius="0"
                                                            :ispreview="false">
                                                        </sa-image>
                                                        <div class="title" :style="{ color: l.title.color }">
                                                            {{ l.title.text }}
                                                        </div>
                                                    </div>
                                                    <div class="sa-flex">
                                                        <div class="tip" :style="{ color: l.tip.color }">
                                                            {{ l.tip.text }}
                                                        </div>
                                                        <el-icon>
                                                            <arrow-right />
                                                        </el-icon>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>

                                        <div v-if="element.type=='menuGrid'" class="menu-grid sa-flex sa-flex-wrap">
                                            <template v-for="item in element.data.list" :key="item">
                                                <div class="menu-item" :style="{width: 100 / element.data.col + '%'}">
                                                    <div v-if="item.badge.show" class="badge"
                                                        :style="{ background: item.badge.bgColor, color: item.badge.color }">
                                                        {{ item.badge.text }}
                                                    </div>
                                                    <sa-image :url="item.src" size="28" radius="0" :ispreview="false">
                                                    </sa-image>
                                                    <div class="title" :style="{ color: item.title.color }">
                                                        {{ item.title.text }}
                                                    </div>
                                                    <div class="tip sa-m-t-6" :style="{ color: item.tip.color }">
                                                        {{ item.tip.text }}
                                                    </div>
                                                </div>
                                            </template>
                                        </div>

                                        <div v-if="element.type=='goodsCard'" class="goods-card">
                                            <div class="card-wrap" :class="`goods-card-${element.data.mode}`" :style="{
                                                margin: `-${element.data.space / 2}px`,
                                            }">
                                                <template v-for="goods in element.data.goodsList" :key="goods">
                                                    <div class="item" :style="{
                                                        'flex-shrink': 0,
                                                        width: `${compNameObj[element.type]?.width[element.data.mode]}`,
                                                        padding: `${element.data.space / 2}px`,
                                                    }">
                                                        <div class="item-wrap" :style="{
                                                                'border-top-left-radius': `${element.data.borderRadiusTop}px`,
                                                                'border-top-right-radius': `${element.data.borderRadiusTop}px`,
                                                                'border-bottom-left-radius': `${element.data.borderRadiusBottom}px`,
                                                                'border-bottom-right-radius': `${element.data.borderRadiusBottom}px`,
                                                                overflow: hidden,
                                                            }">
                                                            <div v-if="element.data.tagStyle.show" class="tag">
                                                                <sa-image :url="element.data.tagStyle.src" radius="0"
                                                                    :ispreview="false">
                                                                </sa-image>
                                                            </div>
                                                            <sa-image :url="goods.image" radius="0" fit="cover"
                                                                :ispreview="false">
                                                            </sa-image>
                                                            <div class="desc">
                                                                <div v-if="element.data.goodsFields.title.show"
                                                                    class="title sa-table-line-1 mb-2"
                                                                    :style="{color: element.data.goodsFields.title.color}">
                                                                    {{ goods.title }}
                                                                </div>
                                                                <div v-if="element.data.goodsFields.subtitle.show"
                                                                    class="subtitle sa-table-line-1 mb-2" :style="{
                                                                    color: element.data.goodsFields.subtitle.color,
                                                                  }">
                                                                    {{ goods.subtitle }}
                                                                </div>
                                                                <el-scrollbar class="promos">
                                                                    <div class="sa-flex mb-2">
                                                                        <div class="promo-tag"
                                                                            v-for="item in goods.promos" :key="item">
                                                                            <span>{{ item.title }}</span>
                                                                        </div>
                                                                    </div>
                                                                </el-scrollbar>
                                                                <div class="sa-flex mb-2">
                                                                    <div v-if="element.data.goodsFields.price.show"
                                                                        class="price mr-1"
                                                                        :style="{color: element.data.goodsFields.price.color}">
                                                                        ￥{{ goods.price[0] }}
                                                                    </div>
                                                                    <s v-if="element.data.goodsFields.original_price.show"
                                                                        class="original-price"
                                                                        :style="{ color: element.data.goodsFields.original_price.color}">
                                                                        ￥{{ goods.original_price }}
                                                                    </s>
                                                                </div>
                                                                <div v-if="element.data.goodsFields.sales.show"
                                                                    class="sales"
                                                                    :style="{color: element.data.goodsFields.sales.color}">
                                                                    已售{{ goods.sales }}件
                                                                </div>
                                                            </div>
                                                            <div class="button" :style="{
                                                              background:
                                                              element.data.buyNowStyle.mode == 1
                                                                  ? 'linear-gradient(90deg,' +
                                                                  element.data.buyNowStyle.color1 +
                                                                    ',' +
                                                                    element.data.buyNowStyle.color2 +
                                                                    ')'
                                                                  : element.data.buyNowStyle.src
                                                                  ? 'url(' + Fast.api.cdnurl(element.data.buyNowStyle.src) + ')'
                                                                  : element.data.buyNowStyle.src,
                                                            }">
                                                                {{ element.data.buyNowStyle.mode == 1 ?
                                                                element.data.buyNowStyle.text : '' }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>

                                        <div v-if="element.type=='goodsShelves'" class="goods-shelves">
                                            <div class="card-wrap" :class="`goods-shelves-${element.data.mode}`" :style="{
                                            margin: `-${element.data.space / 2}px`,
                                            'flex-wrap': `${element.data.mode == 3 ? 'nowrap' : 'wrap'}`,
                                          }">
                                                <template v-for="goods in element.data.goodsList" :key="goods">
                                                    <div class="item" :style="{
                                                    'flex-shrink': 0,
                                                    width: `${compNameObj[element.type]?.width[element.data.mode]}`,
                                                    padding: `${element.data.space / 2}px`,
                                                  }">
                                                        <div class="item-wrap" :style="{
                                                            'border-top-left-radius': `${element.data.borderRadiusTop}px`,
                                                            'border-top-right-radius': `${element.data.borderRadiusTop}px`,
                                                            'border-bottom-left-radius': `${element.data.borderRadiusBottom}px`,
                                                            'border-bottom-right-radius': `${element.data.borderRadiusBottom}px`,
                                                            overflow: hidden,
                                                          }">
                                                            <div v-if="element.data.tagStyle.show" class="tag">
                                                                <sa-image :url="element.data.tagStyle.src" radius="0"
                                                                    fit="cover" :ispreview="false">
                                                                </sa-image>
                                                            </div>
                                                            <sa-image :url="goods.image" radius="0" fit="cover"
                                                                :ispreview="false"></sa-image>
                                                            <div class="desc">
                                                                <div v-if="element.data.goodsFields.title.show"
                                                                    class="title mb-1 sa-table-line-1"
                                                                    :style="{color: element.data.goodsFields.title.color}">
                                                                    {{ goods.title }}
                                                                </div>
                                                                <div v-if="element.data.goodsFields.price.show"
                                                                    class="price"
                                                                    :style="{color: element.data.goodsFields.price.color}">
                                                                    ￥{{ goods.price[0] }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>

                                        <div v-if="element.type=='imageBlock'" class="image-block" :style="{
                                        height: element.style.height + 'px',
                                        'border-top-left-radius': element.data.borderRadiusTop + 'px',
                                        'border-top-right-radius': element.data.borderRadiusTop + 'px',
                                        'border-bottom-left-radius': element.data.borderRadiusBottom + 'px',
                                        'border-bottom-right-radius': element.data.borderRadiusBottom + 'px',
                                        }">
                                            <sa-image :url="element.data.src" fit="fill" radius="0" :ispreview="false">
                                            </sa-image>
                                        </div>

                                        <div v-if="element.type=='imageBanner'" class="image-banner">
                                            <div class="image-banner-wrap" :style="{
                                            margin: `-${element.data.space / 2}px`,
                                          }">
                                                <div class="banner-item" :style="{
                                            padding: `${element.data.space / 2}px`,
                                            height: `${element.style.height - element.style.padding}px`,
                                          }">
                                                    <template v-if="element.data.mode == 1 || element.data.mode == 2">
                                                        <sa-image v-if="element.data.list.length > 0"
                                                            :url="element.data.list[0].src" fit="fill" radius="0"
                                                            :ispreview="false" :style="{
                                                    'border-radius': 0,
                                                    'border-top-left-radius': element.data.borderRadiusTop + 'px',
                                                    'border-top-right-radius': element.data.borderRadiusTop + 'px',
                                                    'border-bottom-left-radius': element.data.borderRadiusBottom + 'px',
                                                    'border-bottom-right-radius': element.data.borderRadiusBottom + 'px',
                                                  }"></sa-image>
                                                    </template>
                                                    <template v-if="element.data.mode == 2">
                                                        <sa-image class="banner-right"
                                                            v-if="element.data.list.length > 1"
                                                            :url="element.data.list[1].src" fit="fill" radius="0"
                                                            :ispreview="false" :style="{
                                                  height: element.style.height - 100 + 'px',
                                                  'margin-top': '-' + (element.style.height - 100) / 2 + 'px',
                                                  'border-radius': 0,
                                                  'border-top-left-radius': element.data.borderRadiusTop + 'px',
                                                  'border-top-right-radius': element.data.borderRadiusTop + 'px',
                                                  'border-bottom-left-radius': element.data.borderRadiusBottom + 'px',
                                                  'border-bottom-right-radius': element.data.borderRadiusBottom + 'px',
                                                }"></sa-image>
                                                    </template>
                                                    <div class="indicator">
                                                        <i :class="`iconfont icondot-${element.data.indicator}`"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div v-if="element.type=='titleBlock'" class="title-block">
                                            <div class="title" :style="{
                                              'align-items': element.data.location == 'center' ? 'center' : 'flex-start',
                                              'margin-left': element.data.skew + 'px',
                                            }">
                                                <div class="sa-m-b-6" :style="{
                                                'font-size': element.data.title.textFontSize + 'px',
                                                color: element.data.title.color,
                                                'font-weight': element.data.title.other.includes('bold') ? 'bold' : '',
                                                'font-style': element.data.title.other.includes('italic') ? 'italic' : '',
                                              }">
                                                    {{ element.data.title.text }}
                                                </div>
                                                <div :style="{
                                                'font-size': element.data.subtitle.textFontSize + 'px',
                                                color: element.data.subtitle.color,
                                                'font-weight': element.data.subtitle.other.includes('bold') ? 'bold' : '',
                                                'font-style': element.data.subtitle.other.includes('italic') ? 'italic' : '',
                                              }">
                                                    {{ element.data.subtitle.text }}
                                                </div>
                                            </div>
                                            <sa-image :url="element.data.src" radius="0" :ispreview="false"></sa-image>
                                            <div v-if="element.data.more.show" class="more sa-flex">
                                                更多
                                                <el-icon>
                                                    <arrow-right />
                                                </el-icon>
                                            </div>
                                        </div>

                                        <div v-if="element.type=='imageCube'" class="image-cube">
                                            <div class="image-cube-wrap" :style="imageCubeStyle(element)">
                                                <div class="image-cube-item" v-for="item in element.data.list"
                                                    :key="item" :style="{
                                            width: item.width * imageCubeScale(element) + 'px',
                                            height: item.height * imageCubeScale(element) + 'px',
                                            top: item.top * imageCubeScale(element) + 'px',
                                            left: item.left * imageCubeScale(element) + 'px',
                                            padding: element.data.space / 2 + 'px',
                                          }">
                                                    <sa-image :url="item.src" radius="0" fit="cover" :ispreview="false"
                                                        :style="{
                                                width: '100%',
                                                height: '100%',
                                                'border-top-left-radius': element.data.borderRadiusTop + 'px',
                                                'border-top-right-radius': element.data.borderRadiusTop + 'px',
                                                'border-bottom-left-radius': element.data.borderRadiusBottom + 'px',
                                                'border-bottom-right-radius': element.data.borderRadiusBottom + 'px',
                                              }"></sa-image>
                                                </div>
                                            </div>
                                        </div>

                                        <div v-if="element.type=='videoPlayer'" class="video-player" :style="{
                                        height: element.style.height + 'px',
                                        }">
                                            <video v-if="!element.data.src" controls>
                                                <source :src="element.data.videoUrl" />
                                            </video>
                                            <sa-image :url="element.data.src" radius="0" fit="fill" :ispreview="false">
                                            </sa-image>
                                        </div>

                                        <div v-if="element.type=='lineBlock'" class="line-block sa-flex">
                                            <div class="line" :style="{
                                            'border-bottom-style': element.data.mode,
                                            'border-bottom-color': element.data.lineColor,
                                          }"></div>
                                        </div>

                                        <div v-if="element.type=='richtext'" class="rich-text">
                                            <div v-if="element.data.richtext" v-html="element.data.richtext.content">
                                            </div>
                                        </div>

                                        <div v-if="element.type=='hotzone'" class="hotzone">
                                            <sa-image :url="element.data.src" fit="fill" radius="0" :ispreview="false"></sa-image>
                                            <div class="map-item sa-flex sa-row-center"
                                                v-for="item in element.data.list" :style="{
                                                    width: `${item.width / 2}px`,
                                                    height: `${item.height / 2}px`,
                                                    top: `${item.top / 2}px`,
                                                    left: `${item.left / 2}px`,
                                                }">
                                                {{ item.name }}
                                            </div>
                                        </div>

                                        <div v-if="element.type=='groupon'" class="groupon">
                                            <div class="card-wrap" :class="[`groupon-${element.data.mode}`]" :style="{
                                                margin: `-${element.data.space / 2}px`,
                                            }">
                                                <template v-for="goods in element.data.goodsList" :key="goods">
                                                    <div class="item" :style="{
                                                        'flex-shrink': 0,
                                                        width: `${element.data.mode == 1 ? '33.3%' : '100%'}`,
                                                        padding: `${element.data.space / 2}px`,
                                                    }">
                                                        <div class="item-wrap" :style="{
                                                            'border-top-left-radius': `${element.data.borderRadiusTop}px`,
                                                            'border-top-right-radius': `${element.data.borderRadiusTop}px`,
                                                            'border-bottom-left-radius': `${element.data.borderRadiusBottom}px`,
                                                            'border-bottom-right-radius': `${element.data.borderRadiusBottom}px`,
                                                            overflow: hidden,
                                                        }">
                                                            <div v-if="element.data.tagStyle.show" class="tag">
                                                                <sa-image :url="element.data.tagStyle.src" radius="0"
                                                                    :ispreview="false">
                                                                </sa-image>
                                                            </div>
                                                            <sa-image :url="goods.image" radius="0" fit="cover"
                                                                :ispreview="false"></sa-image>
                                                            <div v-if="element.data.mode == 1" class="desc">
                                                                <div v-if="element.data.goodsFields.title.show"
                                                                    class="title sa-line-1 mb-1"
                                                                    :style="{color: element.data.goodsFields.title.color}">
                                                                    {{ goods.title }}
                                                                </div>
                                                                <div v-if="element.data.goodsFields.price.show"
                                                                    class="price"
                                                                    :style="{color: element.data.goodsFields.price.color}">
                                                                    ￥{{ goods.price[0] }}
                                                                </div>
                                                            </div>
                                                            <div v-if="element.data.mode == 2" class="desc">
                                                                <div>
                                                                    <div v-if="element.data.goodsFields.title.show"
                                                                        class="title sa-line-2 mb-1"
                                                                        :style="{color: element.data.goodsFields.title.color}">
                                                                        {{ goods.title }}
                                                                    </div>
                                                                    <div v-if="element.data.goodsFields.subtitle.show"
                                                                        class="subtitle sa-line-1"
                                                                        :style="{color: element.data.goodsFields.subtitle.color}">
                                                                        {{ goods.subtitle }}
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <div class="price mb-1">
                                                                        <span v-if="element.data.goodsFields.price.show"
                                                                            :style="{color: element.data.goodsFields.price.color}">￥{{
                                                                            goods.price[0] }}</span>
                                                                        <s v-if="element.data.goodsFields.original_price.show"
                                                                            class="original-price ml-1"
                                                                            :style="{color: element.data.goodsFields.original_price.color}">
                                                                            ￥{{ goods.original_price }}
                                                                        </s>
                                                                    </div>
                                                                    <div class="sa-flex">
                                                                        <div v-if="element.data.goodsFields.sales.show"
                                                                            class="sales"
                                                                            :style="{color: element.data.goodsFields.sales.color}">
                                                                            已售{{goods.sales}}|</div>
                                                                        <div class="stock">库存{{goods.stock}}</div>
                                                                    </div>
                                                                </div>
                                                                <div class="button" :style="{
                                                                    background:
                                                                        element.data.buyNowStyle.mode == 1
                                                                        ? 'linear-gradient(90deg,' +
                                                                            element.data.buyNowStyle.color1 +
                                                                            ',' +
                                                                            element.data.buyNowStyle.color2 +
                                                                            ')'
                                                                        : element.data.buyNowStyle.image
                                                                        ? 'url(' + Fast.api.cdnurl(element.data.buyNowStyle.image) + ')'
                                                                        : element.data.buyNowStyle.image,
                                                                }">
                                                                    {{ element.data.buyNowStyle.mode == 1 ?
                                                                    element.data.buyNowStyle.text : '' }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>

                                        <div v-if="element.type=='seckill'" class="seckill">
                                            <div class="card-wrap" :class="`seckill-${element.data.mode}`" :style="{
                                            margin: `-${element.data.space / 2}px`,
                                          }">
                                                <template v-for="goods in element.data.goodsList" :key="goods">
                                                    <div class="item" :style="{
                                                        'flex-shrink': 0,
                                                        width: `${element.data.mode == 1 ? '33.3%' : '100%'}`,
                                                        padding: `${element.data.space / 2}px`,
                                                    }">
                                                        <div class="item-wrap" :style="{
                                                            'border-top-left-radius': `${element.data.borderRadiusTop}px`,
                                                            'border-top-right-radius': `${element.data.borderRadiusTop}px`,
                                                            'border-bottom-left-radius': `${element.data.borderRadiusBottom}px`,
                                                            'border-bottom-right-radius': `${element.data.borderRadiusBottom}px`,
                                                            overflow: hidden,
                                                        }">
                                                            <div v-if="element.data.tagStyle.show" class="tag">
                                                                <sa-image :url="element.data.tagStyle.src" radius="0"
                                                                    :ispreview="false">
                                                                </sa-image>
                                                            </div>
                                                            <sa-image :url="goods.image" radius="0" fit="cover"
                                                                :ispreview="false"></sa-image>
                                                            <div v-if="element.data.mode == 1" class="desc">
                                                                <div v-if="element.data.goodsFields.title.show"
                                                                    class="title sa-line-1 mb-1"
                                                                    :style="{color: element.data.goodsFields.title.color }">
                                                                    {{ goods.title }}
                                                                </div>
                                                                <div v-if="element.data.goodsFields.price.show"
                                                                    class="price"
                                                                    :style="{color: element.data.goodsFields.price.color}">
                                                                    ￥{{ goods.price[0] }}
                                                                </div>
                                                            </div>
                                                            <div v-if="element.data.mode == 2" class="desc">
                                                                <div>
                                                                    <div v-if="element.data.goodsFields.title.show"
                                                                        class="title sa-line-2 mb-1"
                                                                        :style="{color: element.data.goodsFields.title.color}">
                                                                        {{ goods.title }}
                                                                    </div>
                                                                    <div v-if="element.data.goodsFields.subtitle.show"
                                                                        class="subtitle sa-line-1 mb-2"
                                                                        :style="{color: element.data.goodsFields.subtitle.color}">
                                                                        {{ goods.subtitle }}
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <div class="price mb-1">
                                                                        <span v-if="element.data.goodsFields.price.show"
                                                                            :style="{color: element.data.goodsFields.price.color}">￥{{
                                                                            goods.price[0] }}</span>
                                                                        <s v-if="element.data.goodsFields.original_price.show"
                                                                            class="original-price ml-1"
                                                                            :style="{color: element.data.goodsFields.original_price.color}">
                                                                            ￥{{ goods.original_price }}
                                                                        </s>
                                                                    </div>
                                                                    <div class="sa-flex">
                                                                        <div v-if="element.data.goodsFields.sales.show"
                                                                            class="sales"
                                                                            :style="{color: element.data.goodsFields.sales.color}">
                                                                            已售{{goods.sales}}|</div>
                                                                        <div class="stock">库存{{goods.stock}}</div>
                                                                    </div>
                                                                </div>
                                                                <div class="button" :style="{
                                                                    background:
                                                                        element.data.buyNowStyle.mode == 1
                                                                        ? 'linear-gradient(90deg,' +
                                                                            element.data.buyNowStyle.color1 +
                                                                            ',' +
                                                                            element.data.buyNowStyle.color2 +
                                                                            ')'
                                                                        : element.data.buyNowStyle.image
                                                                        ? 'url(' + Fast.api.cdnurl(element.data.buyNowStyle.image) + ')'
                                                                        : element.data.buyNowStyle.image,
                                                                }">
                                                                    {{ element.data.buyNowStyle.mode == 1 ?
                                                                    element.data.buyNowStyle.text : '' }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>

                                        <div v-if="element.type=='scoreGoods'" class="score-goods">
                                            <div class="card-wrap" :class="`score-goods-${element.data.mode}`" :style="{
                                                margin: `-${element.data.space / 2}px`,
                                            }">
                                                <template v-for="goods in element.data.goodsList" :key="goods">
                                                    <div class="item" :style="{
                                                        'flex-shrink': 0,
                                                        width: `${element.data.mode == 1 ? '50%' : '100%'}`,
                                                        height: 'fit-content',
                                                        padding: `${element.data.space / 2}px`,
                                                    }">
                                                        <div class="item-wrap" :style="{
                                                            'border-top-left-radius': `${element.data.borderRadiusTop}px`,
                                                            'border-top-right-radius': `${element.data.borderRadiusTop}px`,
                                                            'border-bottom-left-radius': `${element.data.borderRadiusBottom}px`,
                                                            'border-bottom-right-radius': `${element.data.borderRadiusBottom}px`,
                                                            overflow: hidden,
                                                        }">
                                                            <sa-image :url="goods.image" radius="0" fit="cover"
                                                                :ispreview="false">
                                                            </sa-image>
                                                            <div class="desc">
                                                                <div>
                                                                    <div v-if="element.data.goodsFields.title.show"
                                                                        class="title mb-1"
                                                                        :class="`sa-line-${element.data.mode}`"
                                                                        :style="{color: element.data.goodsFields.title.color}">
                                                                        {{ goods.title }}
                                                                    </div>
                                                                    <div v-if="element.data.goodsFields.subtitle.show"
                                                                        class="subtitle sa-line-1"
                                                                        :style="{color: element.data.goodsFields.subtitle.color}">
                                                                        {{ goods.subtitle }}
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <div class="sa-flex">
                                                                        <div v-if="element.data.goodsFields.score_price.show"
                                                                            class="score-price sa-flex mb-1"
                                                                            :style="{color: element.data.goodsFields.score_price.color}">
                                                                            <div v-if="Number(goods.score_price.price)">
                                                                                ￥{{ goods.score_price.price }}+
                                                                            </div>
                                                                            <img class="score-icon"
                                                                                src="/assets/addons/shopro/img/decorate/score.png" />
                                                                            {{ goods.score_price.score }}
                                                                        </div>
                                                                    </div>
                                                                    <s v-if="element.data.goodsFields.price.show"
                                                                        class="price mb-1"
                                                                        :style="{color: element.data.goodsFields.price.color}">
                                                                        ￥{{ goods.original_price }}
                                                                    </s>
                                                                    <div class="sales-stock">已售{{ goods.sales
                                                                        }}|库存{{goods.stock }}</div>
                                                                </div>
                                                                <div class="button" :style="{
                                                                    background:
                                                                    element.data.buyNowStyle.mode == 1
                                                                        ? 'linear-gradient(90deg,' +
                                                                        element.data.buyNowStyle.color1 +
                                                                        ',' +
                                                                        element.data.buyNowStyle.color2 +
                                                                        ')'
                                                                        : element.data.buyNowStyle.src
                                                                        ? 'url(' + Fast.api.cdnurl(element.data.buyNowStyle.src) + ')'
                                                                        : element.data.buyNowStyle.src,
                                                                }">
                                                                    {{ element.data.buyNowStyle.mode == 1 ?
                                                                    element.data.buyNowStyle.text : '' }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>

                                        <div v-if="element.type=='mplive'" class="mplive">
                                            <div class="card-wrap" :class="`mplive-${element.data.mode}`" :style="{
                                                margin: `-${element.data.space / 2}px`,
                                            }">
                                                <template v-for="goods in element.data.mpliveList" :key="goods">
                                                    <div class="item" :style="{
                                                        'flex-shrink': 0,
                                                        width: `${compNameObj[element.type]?.width[element.data.mode]}`,
                                                        padding: `${element.data.space / 2}px`,
                                                    }">
                                                        <div class="item-wrap" :style="{
                                                                'border-top-left-radius': `${element.data.borderRadiusTop}px`,
                                                                'border-top-right-radius': `${element.data.borderRadiusTop}px`,
                                                                'border-bottom-left-radius': `${element.data.borderRadiusBottom}px`,
                                                                'border-bottom-right-radius': `${element.data.borderRadiusBottom}px`,
                                                                overflow: hidden,
                                                            }">
                                                            <div class="status sa-flex">
                                                                <img v-if="goods.status==102" class="icon"
                                                                    src="/assets/addons/shopro/img/decorate/mplive-1.png" />
                                                                <img v-if="goods.status==101" class="icon"
                                                                    src="/assets/addons/shopro/img/decorate/mplive-2.png" />
                                                                <img v-if="goods.status==103" class="icon"
                                                                    src="/assets/addons/shopro/img/decorate/mplive-3.png" />
                                                                <div class="tip">{{goods.status_text}}</div>
                                                            </div>
                                                            <sa-image :url="goods.feeds_img" radius="0" fit="cover"
                                                                :ispreview="false">
                                                            </sa-image>
                                                            <div class="bottom"></div>
                                                            <div v-if="element.data.goodsFields.name.show" class="name" :style="{color: element.data.goodsFields.name.color}">
                                                                {{goods.name}}</div>
                                                            <div v-if="element.data.goodsFields.anchor_name.show" class="anchor_name"
                                                                :style="{color: element.data.goodsFields.anchor_name.color}">
                                                                主播：{{goods.anchor_name}}</div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>

                                        <div v-if="element.type=='coupon'" class="coupon">
                                            <div class="card-wrap" :class="`coupon-${element.data.mode}`" :style="{
                                            margin: `-${element.data.space / 2}px`,
                                          }">
                                                <template v-for="(item, index) in element.data.couponList" :key="item">
                                                    <div class="item sa-flex" :style="{
                                                        'flex-shrink': 0,
                                                width: `${compNameObj[element.type]?.width[element.data.mode]}`,
                                                padding: `${element.data.space / 2}px`,
                                              }">
                                                        <template v-if="element.data.mode == 1">
                                                            <div v-if="index < 2" class="item-wrap" :style="{
                                                    color: element.data.fill.color,
                                                    background:
                                                      (element.data.fill.bgImage
                                                        ? 'url(' + Fast.api.cdnurl(element.data.fill.bgImage) + ')'
                                                        : element.data.fill.bgImage) + '100% no-repeat',
                                                    'background-size': '100% 100%',
                                                  }">
                                                                <div>
                                                                    <div class="amount">
                                                                        {{item.type=='discount'?(Number(item.amount).toFixed(0)):item.amount}}<span>{{item.type=='discount'?'折':'元'}}</span>
                                                                    </div>
                                                                    <div class="amount-text">{{
                                                                        item.amount_text }}
                                                                    </div>
                                                                    <div class="time">
                                                                        有效期：{{item.get_start_time.split(' ')[0]}} 至 {{item.get_end_time.split(' ')[0]}}
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <div class="coupon-button" :style="{
                                                        color: element.data.button.color,
                                                        background: `${element.data.button.bgColor}`,
                                                      }">立即领取</div>
                                                                    <div class="stock">仅剩：{{ item.stock }}张
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </template>
                                                        <div v-if="element.data.mode == 2" class="item-wrap" :style="{
                                                    color: element.data.fill.color,
                                                    background:
                                                      (element.data.fill.bgImage
                                                        ? 'url(' + Fast.api.cdnurl(element.data.fill.bgImage) + ')'
                                                        : element.data.fill.bgImage) + '100% no-repeat',
                                                    'background-size': '100% 100%',
                                                  }">
                                                            <div>
                                                                <div class="amount">
                                                                    {{item.type=='discount'?Number(item.amount).toFixed(0):item.amount}}<span>{{item.type=='discount'?'折':'元'}}</span>
                                                                </div>
                                                                <div class="amount-text">{{ item.amount_text }}
                                                                </div>
                                                                <div class="stock">仅剩：{{ item.stock }}张</div>
                                                            </div>
                                                            <div class="coupon-button" :style="{
                                                    color: element.data.button.color,
                                                    background: `${element.data.button.bgColor}`,
                                                  }">立即领取</div>
                                                        </div>
                                                        <div v-if="element.data.mode == 3" class="item-wrap" :style="{
                                                color: element.data.fill.color,
                                                background:
                                                  (element.data.fill.bgImage
                                                    ? 'url(' + Fast.api.cdnurl(element.data.fill.bgImage) + ')'
                                                    : element.data.fill.bgImage) + '100% no-repeat',
                                                'background-size': '100% 100%',
                                              }">
                                                            <div class="amount">
                                                                {{item.type=='discount'?Number(item.amount).toFixed(0):item.amount}}<span>{{item.type=='discount'?'折':'元'}}</span>
                                                            </div>
                                                            <div class="amount-text">{{ item.amount_text }}</div>
                                                            <div class="coupon-button" :style="{
                                                    color: element.data.button.color,
                                                    background: `${element.data.button.bgColor}`,
                                                  }">立即领取</div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="comp-label">{{compNameObj[element.type]?.label}}</div>
                                    <div class="comp-tools">
                                        <el-icon
                                            v-if="(state.pageType == 'home' && index > 0) || (state.pageType == 'user' && index > 1)"
                                            @click.stop="onUpComp(index)">
                                            <arrow-up />
                                        </el-icon>
                                        <el-icon
                                            v-if="index < centerData.templateData[state.pageType].data.length - 1 && !(state.pageType == 'user' && index==0) "
                                            @click.stop="onDownComp(index)">
                                            <arrow-down />
                                        </el-icon>
                                        <el-icon @click.stop="onCopyComp(index)">
                                            <copy-document />
                                        </el-icon>
                                        <el-icon @click.stop="onDeleteComp(index)">
                                            <delete />
                                        </el-icon>
                                    </div>
                                </div>
                            </template>
                        </draggable>
                    </template>
                    <img v-if="state.systemType == 'ios'" class="ios-bar"
                        src="/assets/addons/shopro/img/decorate/ios-bar.png" />
                </div>
            </div>
            <!-- 右侧设置 -->
            <div class="right" :class="!rightData.collapsePanel ? 'is-collapse' : ''">
                <el-scrollbar height="100%" class="right-main">
                    <el-form label-position="right" label-width="60px">
                        <template v-if="currentComp.type">
                            <div class="right-title">
                                <img :src="`/assets/addons/shopro/img/decorate/${currentComp.type}.png`" />
                                <span>{{compNameObj[currentComp.type]?.label}}</span>
                            </div>
                            <div class="right-tab sa-flex">
                                <div v-if="!['userCard','orderCard', 'walletCard', 'couponCard'].includes(currentComp.type)"
                                    class="tab-item" :class="rightData.activeTab=='data'?'is-active':''"
                                    @click="rightData.activeTab='data'">
                                    内容
                                </div>
                                <div v-if="!['floatMenu','popupImage','page'].includes(currentComp.type)" class="tab-item"
                                    :class="rightData.activeTab=='style'?'is-active':''"
                                    @click="rightData.activeTab='style'">
                                    样式
                                </div>
                                <div class="tab-item" :class="rightData.activeTab=='css'?'is-active':''"
                                    @click="rightData.activeTab='css'">
                                    数据
                                </div>
                            </div>
                            <template v-if="currentComp.type=='tabbar'">
                                <template v-if="rightData.activeTab=='data'">
                                    <div class="card">
                                        <div class="title">导航样式</div>
                                        <div class="wrap">
                                            <el-form-item label="选择样式">
                                                <el-radio-group class="custom-radio-button"
                                                    v-model="currentComp.right.mode">
                                                    <el-radio-button :label="1">
                                                        <i class="iconfont icontabbar-1"></i>
                                                    </el-radio-button>
                                                    <el-radio-button :label="2">
                                                        <i class="iconfont icontabbar-2"></i>
                                                    </el-radio-button>
                                                </el-radio-group>
                                            </el-form-item>
                                            <el-form-item label="导航风格">
                                                <el-radio-group v-model="currentComp.right.layout">
                                                    <el-radio :label="1">文字+图片</el-radio>
                                                    <el-radio :label="2">文字</el-radio>
                                                    <el-radio :label="3">图片</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                            <el-form-item label="默认颜色">
                                                <d-color-picker v-model="currentComp.right.inactiveColor">
                                                </d-color-picker>
                                            </el-form-item>
                                            <el-form-item label="选中颜色">
                                                <d-color-picker v-model="currentComp.right.activeColor">
                                                </d-color-picker>
                                            </el-form-item>
                                        </div>
                                    </div>
                                    <d-list v-model="currentComp.right.list"
                                        :item="compNameObj[currentComp.type]?.item">
                                        <template #title>图标设置</template>
                                        <template #listitem="{ element }">
                                            <template
                                                v-if="currentComp.right.layout == 1 || currentComp.right.layout == 3">
                                                <el-form-item label="默认图片">
                                                    <div class="sa-flex">
                                                        <sa-uploader v-model="element.inactiveIcon">
                                                        </sa-uploader>
                                                        <span class="tip">建议尺寸：44*44</span>
                                                    </div>
                                                </el-form-item>
                                                <el-form-item label="选中图片">
                                                    <div class="sa-flex">
                                                        <sa-uploader v-model="element.activeIcon">
                                                        </sa-uploader>
                                                        <span class="tip">建议尺寸：44*44</span>
                                                    </div>
                                                </el-form-item>
                                            </template>
                                            <el-form-item
                                                v-if="currentComp.right.layout == 1 || currentComp.right.layout == 2"
                                                label="文字">
                                                <el-input v-model="element.text"></el-input>
                                            </el-form-item>
                                            <el-form-item label="选择链接">
                                                <d-url v-model="element.url"></d-url>
                                            </el-form-item>
                                        </template>
                                    </d-list>
                                </template>
                                <template v-if="rightData.activeTab=='style'">
                                    <div class="title">组件样式</div>
                                    <div class="wrap">
                                        <el-form-item label="导航背景">
                                            <el-radio-group v-model="currentComp.right.background.type">
                                                <el-radio label="color">纯色</el-radio>
                                                <el-radio label="image">图片</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                        <el-form-item v-if="currentComp.right.background.type == 'color'" label="选择颜色">
                                            <d-color-picker v-model="currentComp.right.background.bgColor">
                                            </d-color-picker>
                                        </el-form-item>
                                        <el-form-item v-if="currentComp.right.background.type == 'image'" label="选择图片">
                                            <sa-uploader v-model="currentComp.right.background.bgImage">
                                            </sa-uploader>
                                        </el-form-item>
                                </template>
                            </template>
                            <template v-if="currentComp.type=='floatMenu'">
                                <template v-if="rightData.activeTab=='data'">
                                    <div class="card">
                                        <div class="title">展示图标</div>
                                        <div class="wrap">
                                            <el-form-item label="状态">
                                                <el-radio-group v-model="currentComp.right.show">
                                                    <el-radio :label="0">关闭</el-radio>
                                                    <el-radio :label="1">开启</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                            <el-form-item label="悬浮样式">
                                                <el-radio-group v-model="currentComp.right.mode">
                                                    <el-radio :label="1">垂直</el-radio>
                                                    <el-radio :label="2">水平</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                            <el-form-item label="显示名称">
                                                <el-radio-group v-model="currentComp.right.isText">
                                                    <el-radio :label="0">不显示</el-radio>
                                                    <el-radio :label="1">显示</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                        </div>
                                    </div>
                                    <d-list v-model="currentComp.right.list"
                                        :item="compNameObj[currentComp.type]?.item">
                                        <template #title>功能图标</template>
                                        <template #listitem="{ element }">
                                            <el-form-item label="按钮图片">
                                                <div class="sa-flex">
                                                    <sa-uploader v-model="element.src"></sa-uploader>
                                                    <span class="tip">建议尺寸：80*80</span>
                                                </div>
                                            </el-form-item>
                                            <el-form-item label="按钮名称">
                                                <d-text-color v-model="element.title" maxlength="4" showWordLimit
                                                    placeholder="请输入按钮名称"></d-text-color>
                                            </el-form-item>
                                            <el-form-item label="按钮链接">
                                                <d-url v-model="element.url"></d-url>
                                            </el-form-item>
                                        </template>
                                    </d-list>
                                </template>
                            </template>
                            <template v-if="currentComp.type=='popupImage'">
                                <template v-if="rightData.activeTab=='data'">
                                    <d-list v-model="currentComp.right.list"
                                        :item="compNameObj[currentComp.type]?.item">
                                        <template #title>展示图标</template>
                                        <template #listitem="{ element }">
                                            <el-form-item label="广告图">
                                                <sa-uploader v-model="element.src"></sa-uploader>
                                            </el-form-item>
                                            <el-form-item label="选择链接">
                                                <d-url v-model="element.url"></d-url>
                                            </el-form-item>
                                            <el-form-item label="显示次数">
                                                <el-radio-group v-model="element.show">
                                                    <el-radio :label="1">仅显示一次</el-radio>
                                                    <el-radio :label="2">每次显示</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                        </template>
                                    </d-list>
                                </template>
                            </template>
                            <template v-if="currentComp.type=='page'">
                                <template v-if="rightData.activeTab=='data'">
                                    <div class="card">
                                        <div class="title">页面背景</div>
                                        <div class="wrap">
                                            <el-form-item label="背景色">
                                                <d-color-picker v-model="currentComp.right.background.color">
                                                </d-color-picker>
                                            </el-form-item>
                                            <el-form-item label="背景图片">
                                                <div class="sa-flex">
                                                    <sa-uploader v-model="currentComp.right.background.src"></sa-uploader>
                                                    <div class="tip">建议宽度：750</div>
                                                </div>
                                            </el-form-item>
                                        </div>
                                    </div>
                                    <div class="card">
                                        <div class="title">头部设置</div>
                                        <div class="wrap">
                                            <el-form-item label="头部样式">
                                                <el-radio-group v-model="currentComp.right.navbar.mode">
                                                    <el-radio label="normal">标准</el-radio>
                                                    <el-radio label="inner">
                                                        沉浸式
                                                        <el-popover popper-class="title-popover" placement="top-start"
                                                            :width="232" trigger="hover"
                                                            :popper-options="{ boundariesElement: 'body' }"
                                                            content="沉侵式头部仅支持微信小程序、APP 建议页面第一个组件为图片展示类组件">
                                                            <template #reference>
                                                                <el-icon class="popover-tip">
                                                                    <Warning />
                                                                </el-icon>
                                                            </template>
                                                        </el-popover>
                                                    </el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                            <el-form-item v-if="currentComp.right.navbar.mode == 'inner'" label="常驻显示">
                                                <el-radio-group v-model="currentComp.right.navbar.alwaysShow">
                                                    <el-radio :label="0">关闭</el-radio>
                                                    <el-radio :label="1">
                                                        开启
                                                        <el-popover popper-class="title-popover" placement="top-start"
                                                            :width="232" trigger="hover"
                                                            :popper-options="{ boundariesElement: 'body' }"
                                                            content="常驻显示关闭后,头部小组件将在页面滑动时淡入">
                                                            <template #reference>
                                                                <el-icon class="popover-tip">
                                                                    <Warning />
                                                                </el-icon>
                                                            </template>
                                                        </el-popover>
                                                    </el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                            <el-form-item label="背景">
                                                <el-radio-group v-model="currentComp.right.navbar.type">
                                                    <el-radio label="color">纯色</el-radio>
                                                    <el-radio label="image">图片</el-radio>
                                                </el-radio-group>
                                            </el-form-item>
                                            <el-form-item v-if="currentComp.right.navbar.type == 'color'" label="选择颜色">
                                                <d-color-picker v-model="currentComp.right.navbar.color"></d-color-picker>
                                            </el-form-item>
                                            <el-form-item v-if="currentComp.right.navbar.type == 'image'" label="选择图片">
                                                <sa-uploader v-model="currentComp.right.navbar.src" fileType="image">
                                                </sa-uploader>
                                            </el-form-item>
                                        </div>
                                    </div>
                                    <div class="card">
                                        <div class="title sa-flex">
                                            头部内容
                                            <div class="warning">
                                                可切换到
                                                {{ state.platformType == 'WechatMiniProgram' ? '其他' : '小程序' }}
                                                平台设置独立样式
                                            </div>
                                        </div>
                                        <div class="wrap">
                                            <d-cube v-model="currentComp.right.navbar.list" :page="state.pageType"
                                                :platform="state.platformType" :type="currentComp.type"
                                                :item="compNameObj[currentComp.type]?.item"
                                                :scale="compNameObj[currentComp.type]?.scale"
                                                :map="compNameObj[currentComp.type]?.map">
                                                <template #item="{item}">
                                                    <template v-if="item">
                                                        <el-form-item label="头部类型">
                                                            <el-radio-group v-model="item.type">
                                                                <el-radio label="text">文字</el-radio>
                                                                <el-radio label="image">图片</el-radio>
                                                                <el-radio label="search">搜索框</el-radio>
                                                            </el-radio-group>
                                                        </el-form-item>
                                                        <template v-if="item.type == 'text'">
                                                            <el-form-item label="文字内容">
                                                                <el-input v-model="item.text" maxlength="10"
                                                                    show-word-limit>
                                                                </el-input>
                                                            </el-form-item>
                                                            <el-form-item label="文字颜色">
                                                                <d-color-picker v-model="item.textColor">
                                                                </d-color-picker>
                                                            </el-form-item>
                                                        </template>
                                                        <template v-if="item.type == 'image'">
                                                            <el-form-item label="上传图片">
                                                                <div class="sa-flex">
                                                                    <sa-uploader v-model="item.src" fileType="image">
                                                                    </sa-uploader>
                                                                    <div class="tip">建议尺寸：56*56</div>
                                                                </div>
                                                            </el-form-item>
                                                            <el-form-item label="选择链接">
                                                                <d-url v-model="item.url"></d-url>
                                                            </el-form-item>
                                                        </template>
                                                        <template v-if="item.type == 'search'">
                                                            <el-form-item label="提示文字">
                                                                <el-input v-model="item.placeholder" maxlength="10"
                                                                    show-word-limit>
                                                                </el-input>
                                                            </el-form-item>
                                                            <el-form-item label="圆角">
                                                                <d-slider v-model="item.borderRadius">
                                                                </d-slider>
                                                            </el-form-item>
                                                        </template>
                                                    </template>
                                                </template>
                                            </d-cube>
                                        </div>
                                    </div>
                                </template>
                            </template>
                            <template
                                v-if="currentComp.type!='tabbar' && currentComp.type!='floatMenu' && currentComp.type!='popupImage' && currentComp.type!='page'">
                                <template v-if="rightData.activeTab=='data'">
                                    <template v-if="currentComp.type=='searchBlock'">
                                        <div class="card">
                                            <div class="title">默认文字</div>
                                            <div class="wrap">
                                                <el-form-item label="提示内容">
                                                    <el-input v-model="currentComp.right.data.placeholder"
                                                        placeholder="请输入提示内容"></el-input>
                                                </el-form-item>
                                                <el-form-item label="圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadius"></d-slider>
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <d-list v-model="currentComp.right.data.keywords"
                                            :item="compNameObj[currentComp.type]?.item" :leng="3">
                                            <template #title>
                                                搜索关键字
                                                <div class="warning">最多可创建三个</div>
                                            </template>
                                            <template #listitem="{ element }">
                                                <el-form-item label="关键字">
                                                    <d-text-color v-model="element" maxlength="4" showWordLimit
                                                        placeholder="请输入按钮名称"></d-text-color>
                                                </el-form-item>
                                            </template>
                                        </d-list>
                                    </template>
                                    <template v-if="currentComp.type=='noticeBlock'">
                                        <div class="card">
                                            <div class="title">公告图标</div>
                                            <div class="wrap">
                                                <el-form-item label="公告样式">
                                                    <el-radio-group v-model="currentComp.right.data.mode">
                                                        <el-radio :label="1">系统图标</el-radio>
                                                        <el-radio :label="2">自定义</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item v-if="currentComp.right.data.mode == 1" label="公告图">
                                                    <div class="sa-flex">
                                                        <img class="notice-item"
                                                            :class="currentComp.right.data.src == item?'is-active':''"
                                                            v-for="item in rightData.noticeList" :src="item"
                                                            @click="currentComp.right.data.src = item" />
                                                    </div>
                                                </el-form-item>
                                                <el-form-item v-if="currentComp.right.data.mode == 2" label="图片">
                                                    <sa-uploader v-model="currentComp.right.data.src"></sa-uploader>
                                                    <div class="tip">建议尺寸：24*24</div>
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">内容设置</div>
                                            <div class="wrap">
                                                <el-form-item label="公告内容">
                                                    <d-text-color v-model="currentComp.right.data.title"
                                                        placeholder="请输入公告内容">
                                                    </d-text-color>
                                                </el-form-item>
                                                <el-form-item label="链接">
                                                    <d-url v-model="currentComp.right.data.url"></d-url>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='menuButton'">
                                        <div class="card">
                                            <div class="title">样式选择</div>
                                            <div class="wrap">
                                                <el-form-item label="菜单布局">
                                                    <el-radio-group v-model="currentComp.right.data.layout">
                                                        <el-radio :label="1">图片+文字</el-radio>
                                                        <el-radio :label="2">图片</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item label="列数">
                                                    <el-radio-group v-model="currentComp.right.data.col">
                                                        <el-radio :label="3">3个</el-radio>
                                                        <el-radio :label="4">4个</el-radio>
                                                        <el-radio :label="5">5个</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item label="行数">
                                                    <el-radio-group v-model="currentComp.right.data.row">
                                                        <el-radio :label="1">1行</el-radio>
                                                        <el-radio :label="2">2行</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <d-list v-model="currentComp.right.data.list"
                                            :item="compNameObj[currentComp.type]?.item">
                                            <template #title>图标设置</template>
                                            <template #listitem="{ element }">
                                                <el-form-item label="图标">
                                                    <sa-uploader v-model="element.src"></sa-uploader>
                                                    <div class="tip">建议尺寸：98*98</div>
                                                </el-form-item>
                                                <el-form-item label="标题">
                                                    <d-text-color v-model="element.title" placeholder="请输入标题"
                                                        maxlength="4" show-word-limit></d-text-color>
                                                </el-form-item>
                                                <el-form-item label="链接">
                                                    <d-url v-model="element.url"></d-url>
                                                </el-form-item>
                                                <el-form-item label="标签">
                                                    <el-switch v-model="element.badge.show" :active-value="1"
                                                        :inactive-value="0" />
                                                </el-form-item>
                                                <template v-if="element.badge.show">
                                                    <el-form-item label="标签内容">
                                                        <d-text-color v-model="element.badge" placeholder="请输入标签内容"
                                                            maxlength="4" show-word-limit></d-text-color>
                                                    </el-form-item>
                                                    <el-form-item label="标签背景">
                                                        <d-color-picker v-model="element.badge.bgColor">
                                                        </d-color-picker>
                                                    </el-form-item>
                                                </template>
                                            </template>
                                        </d-list>
                                    </template>
                                    <template v-if="currentComp.type=='menuList'">
                                        <d-list v-model="currentComp.right.data.list"
                                            :item="compNameObj[currentComp.type]?.item">
                                            <template #title>菜单设置</template>
                                            <template #listitem="{ element }">
                                                <el-form-item label="图标">
                                                    <sa-uploader v-model="element.src"></sa-uploader>
                                                    <div class="tip">建议尺寸：44*44</div>
                                                </el-form-item>
                                                <el-form-item label="标题">
                                                    <d-text-color v-model="element.title" placeholder="请输入标题"
                                                        maxlength="4" show-word-limit></d-text-color>
                                                </el-form-item>
                                                <el-form-item label="功能提示">
                                                    <d-text-color v-model="element.tip" placeholder="请输入功能提示"
                                                        maxlength="4" show-word-limit></d-text-color>
                                                </el-form-item>
                                                <el-form-item label="链接">
                                                    <d-url v-model="element.url"></d-url>
                                                </el-form-item>
                                            </template>
                                        </d-list>
                                    </template>
                                    <template v-if="currentComp.type=='menuGrid'">
                                        <div class="card">
                                            <div class="title">样式选择</div>
                                            <div class="wrap">
                                                <el-form-item label="每行数量">
                                                    <el-radio-group v-model="currentComp.right.data.col">
                                                        <el-radio :label="3">3个</el-radio>
                                                        <el-radio :label="4">4个</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <d-list v-model="currentComp.right.data.list"
                                            :item="compNameObj[currentComp.type]?.item">
                                            <template #title>图标设置</template>
                                            <template #listitem="{ element }">
                                                <el-form-item label="图标">
                                                    <sa-uploader v-model="element.src"></sa-uploader>
                                                    <div class="tip">建议尺寸：44*44</div>
                                                </el-form-item>
                                                <el-form-item label="标题">
                                                    <d-text-color v-model="element.title" placeholder="请输入标题"
                                                        maxlength="4" show-word-limit></d-text-color>
                                                </el-form-item>
                                                <el-form-item label="功能提示">
                                                    <d-text-color v-model="element.tip" placeholder="请输入功能提示"
                                                        maxlength="4" show-word-limit></d-text-color>
                                                </el-form-item>
                                                <el-form-item label="链接">
                                                    <d-url v-model="element.url"></d-url>
                                                </el-form-item>
                                                <el-form-item label="标签">
                                                    <el-switch v-model="element.badge.show" :active-value="1"
                                                        :inactive-value="0" />
                                                </el-form-item>
                                                <template v-if="element.badge.show">
                                                    <el-form-item label="标签内容">
                                                        <d-text-color v-model="element.badge" placeholder="请输入标签内容"
                                                            maxlength="4" show-word-limit></d-text-color>
                                                    </el-form-item>
                                                    <el-form-item label="标签背景">
                                                        <d-color-picker v-model="element.badge.bgColor">
                                                        </d-color-picker>
                                                    </el-form-item>
                                                </template>
                                            </template>
                                        </d-list>
                                    </template>
                                    <template v-if="currentComp.type=='goodsCard'">
                                        <d-goods-select v-model="currentComp.right.data.goodsList" :multiple="true">
                                        </d-goods-select>
                                        <div class="card">
                                            <div class="title">商品样式</div>
                                            <div class="wrap">
                                                <el-form-item label="选择风格">
                                                    <el-radio-group class="custom-radio-button"
                                                        v-model="currentComp.right.data.mode">
                                                        <el-radio-button :label="1">
                                                            <i class="iconfont iconmode-1"></i>
                                                        </el-radio-button>
                                                        <el-radio-button :label="2">
                                                            <i class="iconfont iconmode-2"></i>
                                                        </el-radio-button>
                                                        <el-radio-button :label="3">
                                                            <i class="iconfont iconmode-3"></i>
                                                        </el-radio-button>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <template v-for="(item, field) in currentComp.right.data.goodsFields"
                                                    :key="field">
                                                    <template v-if="compNameObj[currentComp.type]?.fieldLabel[field]">
                                                        <el-form-item
                                                            :label="compNameObj[currentComp.type]?.fieldLabel[field]">
                                                            <d-color-picker v-model="item.color"
                                                                v-model:show="item.show" :isshow="true">
                                                            </d-color-picker>
                                                        </el-form-item>
                                                    </template>
                                                </template>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">商品角标</div>
                                            <div class="wrap">
                                                <el-form-item label="角标选择">
                                                    <el-radio-group v-model="currentComp.right.data.tagStyle.show">
                                                        <el-radio :label="0">不显示</el-radio>
                                                        <el-radio :label="1">显示</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item v-if="currentComp.right.data.tagStyle.show" label="上传图片">
                                                    <sa-uploader v-model="currentComp.right.data.tagStyle.src">
                                                    </sa-uploader>
                                                    <div class="tip">建议尺寸：36*22</div>
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">加购设置</div>
                                            <div class="wrap">
                                                <el-form-item label="加购按钮">
                                                    <el-radio-group v-model="currentComp.right.data.buyNowStyle.mode">
                                                        <el-radio :label="1">文字</el-radio>
                                                        <el-radio :label="2">图片</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <template v-if="currentComp.right.data.buyNowStyle.mode == 1">
                                                    <el-form-item label="文字">
                                                        <el-input v-model="currentComp.right.data.buyNowStyle.text">
                                                        </el-input>
                                                    </el-form-item>
                                                    <el-form-item label="背景1">
                                                        <d-color-picker
                                                            v-model="currentComp.right.data.buyNowStyle.color1">
                                                        </d-color-picker>
                                                    </el-form-item>
                                                    <el-form-item label="背景2">
                                                        <d-color-picker
                                                            v-model="currentComp.right.data.buyNowStyle.color2">
                                                        </d-color-picker>
                                                    </el-form-item>
                                                </template>
                                                <el-form-item v-if="currentComp.right.data.buyNowStyle.mode == 2"
                                                    label="图片">
                                                    <div class="sa-flex">
                                                        <sa-uploader v-model="currentComp.right.data.buyNowStyle.src">
                                                        </sa-uploader>
                                                        <div class="tip">建议尺寸：56*56</div>
                                                    </div>
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">样式</div>
                                            <div class="wrap">
                                                <el-form-item label="上圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusTop">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="下圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusBottom">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="间距">
                                                    <d-slider v-model="currentComp.right.data.space"></d-slider>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='goodsShelves'">
                                        <d-goods-select v-model="currentComp.right.data.goodsList" :multiple="true">
                                        </d-goods-select>
                                        <div class="card">
                                            <div class="title">商品样式</div>
                                            <div class="wrap">
                                                <el-form-item label="选择风格">
                                                    <el-radio-group class="custom-radio-button"
                                                        v-model="currentComp.right.data.mode">
                                                        <el-radio-button :label="1">
                                                            <i class="iconfont iconmode-4"></i>
                                                        </el-radio-button>
                                                        <el-radio-button :label="2">
                                                            <i class="iconfont iconmode-2"></i>
                                                        </el-radio-button>
                                                        <el-radio-button :label="3">
                                                            <i class="iconfont iconmode-5"></i>
                                                        </el-radio-button>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <template v-for="(item, field) in currentComp.right.data.goodsFields"
                                                    :key="field">
                                                    <template v-if="compNameObj[currentComp.type]?.fieldLabel[field]">
                                                        <el-form-item
                                                            :label="compNameObj[currentComp.type]?.fieldLabel[field]">
                                                            <d-color-picker v-model="item.color"
                                                                v-model:show="item.show" :isshow="true">
                                                            </d-color-picker>
                                                        </el-form-item>
                                                    </template>
                                                </template>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">商品角标</div>
                                            <div class="wrap">
                                                <el-form-item label="角标选择">
                                                    <el-radio-group v-model="currentComp.right.data.tagStyle.show">
                                                        <el-radio :label="0">不显示</el-radio>
                                                        <el-radio :label="1">显示</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item v-if="currentComp.right.data.tagStyle.show" label="上传图片">
                                                    <div class="sa-flex">
                                                        <sa-uploader v-model="currentComp.right.data.tagStyle.src">
                                                        </sa-uploader>
                                                        <div class="tip">建议尺寸：36*22</div>
                                                    </div>
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">样式</div>
                                            <div class="wrap">
                                                <el-form-item label="上圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusTop">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="下圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusBottom">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="间距">
                                                    <d-slider v-model="currentComp.right.data.space"></d-slider>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='imageBlock'">
                                        <div class="card">
                                            <div class="title">添加图片</div>
                                            <div class="wrap">
                                                <el-form-item label="上传图片">
                                                    <sa-uploader v-model="currentComp.right.data.src"></sa-uploader>
                                                    <div class="tip">建议宽度：750</div>
                                                </el-form-item>
                                                <el-form-item label="链接">
                                                    <d-url v-model="currentComp.right.data.url"></d-url>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='imageBanner'">
                                        <div class="card">
                                            <div class="title">样式设置</div>
                                            <div class="wrap">
                                                <!-- <el-form-item label="选择样式">
                                                    <el-radio-group class="custom-radio-button"
                                                        v-model="currentComp.right.data.mode">
                                                        <el-radio-button :label="1">
                                                            <i class="iconfont iconmode-6"></i>
                                                        </el-radio-button>
                                                        <el-radio-button :label="2">
                                                            <i class="iconfont iconmode-7"></i>
                                                        </el-radio-button>
                                                    </el-radio-group>
                                                </el-form-item> -->
                                                <el-form-item label="Dot样式">
                                                    <el-radio-group class="custom-radio-button"
                                                        v-model="currentComp.right.data.indicator">
                                                        <el-radio-button :label="1">
                                                            <i class="iconfont icondot-1"></i>
                                                        </el-radio-button>
                                                        <el-radio-button :label="2">
                                                            <i class="iconfont icondot-2"></i>
                                                        </el-radio-button>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item label="间距">
                                                    <d-slider v-model="currentComp.right.data.space"></d-slider>
                                                </el-form-item>
                                                <el-form-item label="是否轮播">
                                                    <el-switch v-model="currentComp.right.data.autoplay" />
                                                  </el-form-item>
                                                  <el-form-item v-if="currentComp.right.data.autoplay" label="时间间隔">
                                                    <el-input v-model="currentComp.right.data.interval">
                                                      <template #append>ms</template>
                                                    </el-input>
                                                  </el-form-item>
                                            </div>
                                        </div>
                                        <d-list v-model="currentComp.right.data.list"
                                            :item="compNameObj[currentComp.type]?.item">
                                            <template #title>图片上传</template>
                                            <template #listitem="{ element }">
                                                <el-form-item label="标题">
                                                    <el-input v-model="element.title" placeholder="请输入标题"></el-input>
                                                </el-form-item>
                                                <el-form-item label="选择类型">
                                                    <el-radio-group v-model="element.type">
                                                        <el-radio label="image">图片</el-radio>
                                                        <el-radio label="video">视频</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item label="上传">
                                                    <sa-uploader v-model="element.src"></sa-uploader>
                                                </el-form-item>
                                                <el-form-item v-if="element.type == 'video'" label="视频封面">
                                                    <sa-uploader v-model="element.poster"></sa-uploader>
                                                </el-form-item>
                                                <el-form-item label="链接">
                                                    <d-url v-model="element.url"></d-url>
                                                </el-form-item>
                                            </template>
                                        </d-list>
                                    </template>
                                    <template v-if="currentComp.type=='titleBlock'">
                                        <div class="card">
                                            <div class="title">
                                                选择风格 <div class="tip">建议尺寸：750*80</div>
                                            </div>
                                            <div class="wrap">
                                                <el-form-item label="标题样式">
                                                    <el-button @click="titleBlockDialog.visible = true">选择样式</el-button>
                                                </el-form-item>
                                                <el-form-item label="背景图片">
                                                    <sa-image class="title-block-image"
                                                        :url="currentComp.right.data.src" :ispreview="false"
                                                        @click="onSelectTitleBlockImage"></sa-image>
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">显示设置</div>
                                            <div class="wrap">
                                                <el-form-item label="位置">
                                                    <el-radio-group v-model="currentComp.right.data.location">
                                                        <el-radio label="left">居左</el-radio>
                                                        <el-radio label="center">居中</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item label="偏移">
                                                    <el-input-number v-model="currentComp.right.data.skew" />
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">标题设置</div>
                                            <div class="wrap">
                                                <el-form-item label="文字">
                                                    <d-text-color v-model="currentComp.right.data.title"
                                                        placeholder="请输入文字">
                                                    </d-text-color>
                                                </el-form-item>
                                                <el-form-item label="字号">
                                                    <d-slider v-model="currentComp.right.data.title.textFontSize">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="其他">
                                                    <el-checkbox-group v-model="currentComp.right.data.title.other">
                                                        <el-checkbox label="bold">加粗</el-checkbox>
                                                        <el-checkbox label="italic">倾斜</el-checkbox>
                                                    </el-checkbox-group>
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">副标题设置</div>
                                            <div class="wrap">
                                                <el-form-item label="文字">
                                                    <d-text-color v-model="currentComp.right.data.subtitle"
                                                        placeholder="请输入文字">
                                                    </d-text-color>
                                                </el-form-item>
                                                <el-form-item label="字号">
                                                    <d-slider v-model="currentComp.right.data.subtitle.textFontSize">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="其他">
                                                    <el-checkbox-group v-model="currentComp.right.data.subtitle.other">
                                                        <el-checkbox label="bold">加粗</el-checkbox>
                                                        <el-checkbox label="italic">倾斜</el-checkbox>
                                                    </el-checkbox-group>
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">更多设置</div>
                                            <div class="wrap">
                                                <el-form-item label="显示更多">
                                                    <el-radio-group v-model="currentComp.right.data.more.show">
                                                        <el-radio :label="0">不显示</el-radio>
                                                        <el-radio :label="1">显示</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item v-if="currentComp.right.data.more.show" label="链接">
                                                    <d-url v-model="currentComp.right.data.more.url"></d-url>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='imageCube'">
                                        <div class="card">
                                            <div class="title">
                                                魔方样式 <div class="tip">每格尺寸：187*187</div>
                                            </div>
                                            <div class="wrap">
                                                <d-cube v-model="currentComp.right.data.list" :page="state.pageType"
                                                    :platform="state.platformType" :type="currentComp.type"
                                                    :item="compNameObj[currentComp.type]?.item">
                                                    <template #item="{item}">
                                                        <template v-if="item">
                                                            <el-form-item label="上传图片">
                                                                <sa-uploader v-model="item.src">
                                                                </sa-uploader>
                                                            </el-form-item>
                                                            <el-form-item label="链接">
                                                                <d-url v-model="item.url">
                                                                </d-url>
                                                            </el-form-item>
                                                        </template>
                                                    </template>
                                                </d-cube>
                                                <el-form-item label="上圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusTop">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="下圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusBottom">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="间距">
                                                    <d-slider v-model="currentComp.right.data.space"></d-slider>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='videoPlayer'">
                                        <div class="card">
                                            <div class="title">内容设置</div>
                                            <div class="wrap">
                                                <el-form-item label="视频链接">
                                                    <el-input v-model="currentComp.right.data.videoUrl">
                                                        <template #append>
                                                            <span class="cursor-pointer"
                                                                @click="onSelectVideo">选择</span>
                                                        </template>
                                                    </el-input>
                                                </el-form-item>
                                                <el-form-item label="视频封面">
                                                    <div class="sa-flex">
                                                        <sa-uploader v-model="currentComp.right.data.src"></sa-uploader>
                                                    </div>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='lineBlock'">
                                        <div class="card">
                                            <div class="title">内容设置</div>
                                            <div class="wrap">
                                                <el-form-item label="选择风格">
                                                    <el-radio-group class="custom-radio-button"
                                                        v-model="currentComp.right.data.mode">
                                                        <el-radio-button label="solid">
                                                            <i class="iconfont iconline-1"></i>
                                                        </el-radio-button>
                                                        <el-radio-button label="dotted">
                                                            <i class="iconfont iconline-2"></i>
                                                        </el-radio-button>
                                                        <el-radio-button label="dashed">
                                                            <i class="iconfont iconline-3"></i>
                                                        </el-radio-button>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item label="线条颜色">
                                                    <d-color-picker v-model="currentComp.right.data.lineColor">
                                                    </d-color-picker>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='richtext'">
                                        <div class="card">
                                            <div class="title">富文本样式</div>
                                            <div class="wrap">
                                                <el-form-item label="富文本">
                                                    <el-input v-model="currentComp.right.data.title">
                                                        <template #append>
                                                            <span class="cursor-pointer"
                                                                @click="onSelectRichtext">选择</span>
                                                        </template>
                                                    </el-input>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='hotzone'">
                                        <div class="card">
                                            <div class="title">添加图片</div>
                                            <div class="content">
                                                <el-form-item label="上传图片">
                                                    <div class="sa-flex">
                                                        <sa-uploader v-model="currentComp.right.data.src"
                                                            fileType="image"></sa-uploader>
                                                        <div class="tip">建议宽度：750</div>
                                                    </div>
                                                </el-form-item>
                                                <el-button v-if="currentComp.right.data.src" class="add-button"
                                                    icon="Plus" @click="onSetHotzone">设置热区</el-button>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='groupon'">
                                        <d-list v-model="currentComp.right.data.activityList" :leng="1">
                                            <template #title>选择活动</template>
                                            <template #deleteIcon>
                                                <span class="list-delete" @click="onDeleteGroupon">删除</span>
                                            </template>
                                            <template #listitem="{ element }">
                                                <div>
                                                    <div class="name sa-m-b-4">
                                                        {{ element.title }}
                                                    </div>
                                                    <div class="amount-text"> {{
                                                        currentComp.right.data?.goodsList.length }}
                                                        件商品
                                                    </div>
                                                </div>
                                            </template>
                                            <template #add>
                                                <el-button class="add-button" icon="Plus" @click="onSelectGroupon">添加
                                                </el-button>
                                            </template>
                                        </d-list>
                                        <div class="card">
                                            <div class="title">商品样式</div>
                                            <div class="wrap">
                                                <el-form-item label="选择风格">
                                                    <el-radio-group class="custom-radio-button"
                                                        v-model="currentComp.right.data.mode">
                                                        <el-radio-button :label="1">
                                                            <i class="iconfont iconmode-2"></i>
                                                        </el-radio-button>
                                                        <el-radio-button :label="2">
                                                            <i class="iconfont iconmode-3"></i>
                                                        </el-radio-button>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <template v-for="(item, field) in currentComp.right.data.goodsFields"
                                                    :key="field">
                                                    <template
                                                        v-if="compNameObj[currentComp.type]?.fieldLabel[currentComp.right.data.mode][field]">
                                                        <el-form-item
                                                            :label="compNameObj[currentComp.type]?.fieldLabel[currentComp.right.data.mode][field]">
                                                            <d-color-picker v-model="item.color"
                                                                v-model:show="item.show" :isshow="true">
                                                            </d-color-picker>
                                                        </el-form-item>
                                                    </template>
                                                </template>
                                            </div>
                                        </div>
                                        <template v-if="currentComp.right.data.mode == 2">
                                            <div class="card">
                                                <div class="title">加购设置</div>
                                                <div class="wrap">
                                                    <el-form-item label="加购按钮">
                                                        <el-radio-group
                                                            v-model="currentComp.right.data.buyNowStyle.mode">
                                                            <el-radio :label="1">文字</el-radio>
                                                            <el-radio :label="2">图片</el-radio>
                                                        </el-radio-group>
                                                    </el-form-item>
                                                    <template v-if="currentComp.right.data.buyNowStyle.mode == 1">
                                                        <el-form-item label="文字">
                                                            <el-input v-model="currentComp.right.data.buyNowStyle.text">
                                                            </el-input>
                                                        </el-form-item>
                                                        <el-form-item label="背景1">
                                                            <d-color-picker
                                                                v-model="currentComp.right.data.buyNowStyle.color1">
                                                            </d-color-picker>
                                                        </el-form-item>
                                                        <el-form-item label="背景2">
                                                            <d-color-picker
                                                                v-model="currentComp.right.data.buyNowStyle.color2">
                                                            </d-color-picker>
                                                        </el-form-item>
                                                    </template>
                                                    <el-form-item v-if="currentComp.right.data.buyNowStyle.mode == 2"
                                                        label="图片">
                                                        <sa-uploader v-model="currentComp.right.data.buyNowStyle.src">
                                                        </sa-uploader>
                                                    </el-form-item>
                                                </div>
                                            </div>
                                        </template>
                                        <div class="card">
                                            <div class="title">商品角标</div>
                                            <div class="wrap">
                                                <el-form-item label="角标选择">
                                                    <el-radio-group v-model="currentComp.right.data.tagStyle.show">
                                                        <el-radio :label="0">不显示</el-radio>
                                                        <el-radio :label="1">显示</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item v-if="currentComp.right.data.tagStyle.show == 1"
                                                    label="上传图片">
                                                    <div class="sa-flex">
                                                        <sa-uploader v-model="currentComp.right.data.tagStyle.src">
                                                        </sa-uploader>
                                                        <span class="tip">建议尺寸：36*22</span>
                                                    </div>
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">样式</div>
                                            <div class="wrap">
                                                <el-form-item label="上圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusTop">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="下圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusBottom">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="间距">
                                                    <d-slider v-model="currentComp.right.data.space"></d-slider>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='seckill'">
                                        <d-list v-model="currentComp.right.data.activityList" :leng="1">
                                            <template #title>选择活动</template>
                                            <template #deleteIcon>
                                                <span class="list-delete" @click="onDeleteSeckill">删除</span>
                                            </template>
                                            <template #listitem="{ element }">
                                                <div>
                                                    <div class="name">
                                                        {{ element.title }}
                                                    </div>
                                                    <div class="amount-text"> {{
                                                        currentComp.right.data?.goodsList.length }}
                                                        件商品
                                                    </div>
                                                </div>
                                            </template>
                                            <template #add>
                                                <el-button class="add-button" icon="Plus" @click="onSelectSeckill">添加
                                                </el-button>
                                            </template>
                                        </d-list>
                                        <div class="card">
                                            <div class="title">商品样式</div>
                                            <div class="wrap">
                                                <el-form-item label="选择风格">
                                                    <el-radio-group class="custom-radio-button"
                                                        v-model="currentComp.right.data.mode">
                                                        <el-radio-button :label="1">
                                                            <i class="iconfont iconmode-2"></i>
                                                        </el-radio-button>
                                                        <el-radio-button :label="2">
                                                            <i class="iconfont iconmode-3"></i>
                                                        </el-radio-button>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <template v-for="(item, field) in currentComp.right.data.goodsFields"
                                                    :key="field">
                                                    <template
                                                        v-if="compNameObj[currentComp.type]?.fieldLabel[currentComp.right.data.mode][field]">
                                                        <el-form-item
                                                            :label="compNameObj[currentComp.type]?.fieldLabel[currentComp.right.data.mode][field]">
                                                            <d-color-picker v-model="item.color"
                                                                v-model:show="item.show" :isshow="true">
                                                            </d-color-picker>
                                                        </el-form-item>
                                                    </template>
                                                </template>
                                            </div>
                                        </div>
                                        <template v-if="currentComp.right.data.mode == 2">
                                            <div class="card">
                                                <div class="title">加购设置</div>
                                                <div class="wrap">
                                                    <el-form-item label="加购按钮">
                                                        <el-radio-group
                                                            v-model="currentComp.right.data.buyNowStyle.mode">
                                                            <el-radio :label="1">文字</el-radio>
                                                            <el-radio :label="2">图片</el-radio>
                                                        </el-radio-group>
                                                    </el-form-item>
                                                    <template v-if="currentComp.right.data.buyNowStyle.mode == 1">
                                                        <el-form-item label="文字">
                                                            <el-input v-model="currentComp.right.data.buyNowStyle.text">
                                                            </el-input>
                                                        </el-form-item>
                                                        <el-form-item label="背景1">
                                                            <d-color-picker
                                                                v-model="currentComp.right.data.buyNowStyle.color1">
                                                            </d-color-picker>
                                                        </el-form-item>
                                                        <el-form-item label="背景2">
                                                            <d-color-picker
                                                                v-model="currentComp.right.data.buyNowStyle.color2">
                                                            </d-color-picker>
                                                        </el-form-item>
                                                    </template>
                                                    <el-form-item v-if="currentComp.right.data.buyNowStyle.mode == 2"
                                                        label="图片">
                                                        <sa-uploader v-model="currentComp.right.data.buyNowStyle.src">
                                                        </sa-uploader>
                                                    </el-form-item>
                                                </div>
                                            </div>
                                        </template>
                                        <div class="card">
                                            <div class="title">商品角标</div>
                                            <div class="wrap">
                                                <el-form-item label="角标选择">
                                                    <el-radio-group v-model="currentComp.right.data.tagStyle.show">
                                                        <el-radio :label="0">不显示</el-radio>
                                                        <el-radio :label="1">显示</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item v-if="currentComp.right.data.tagStyle.show == 1"
                                                    label="上传图片">
                                                    <div class="sa-flex">
                                                        <sa-uploader v-model="currentComp.right.data.tagStyle.src">
                                                        </sa-uploader>
                                                        <span class="tip">建议尺寸：36*22</span>
                                                    </div>
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">样式</div>
                                            <div class="wrap">
                                                <el-form-item label="上圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusTop">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="下圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusBottom">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="间距">
                                                    <d-slider v-model="currentComp.right.data.space"></d-slider>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='scoreGoods'">
                                        <d-goods-select v-model="currentComp.right.data.goodsList" :multiple="true">
                                            <template #add>
                                                <el-button class="add-button sa-m-0 sa-m-b-16" icon="Plus"
                                                    @click="onSelectScoreGoods">添加
                                                </el-button>
                                            </template>
                                        </d-goods-select>
                                        <div class="card">
                                            <div class="title">商品样式</div>
                                            <div class="wrap">
                                                <el-form-item label="选择风格">
                                                    <el-radio-group class="custom-radio-button"
                                                        v-model="currentComp.right.data.mode">
                                                        <el-radio-button :label="1">
                                                            <i class="iconfont iconmode-2"></i>
                                                        </el-radio-button>
                                                        <el-radio-button :label="2">
                                                            <i class="iconfont iconmode-3"></i>
                                                        </el-radio-button>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <template v-for="(item, field) in currentComp.right.data.goodsFields"
                                                    :key="field">
                                                    <template v-if="compNameObj[currentComp.type]?.fieldLabel[field]">
                                                        <el-form-item
                                                            :label="compNameObj[currentComp.type]?.fieldLabel[field]">
                                                            <d-color-picker v-model="item.color"
                                                                v-model:show="item.show" :isshow="true">
                                                            </d-color-picker>
                                                        </el-form-item>
                                                    </template>
                                                </template>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">加购设置</div>
                                            <div class="wrap">
                                                <el-form-item label="加购按钮">
                                                    <el-radio-group v-model="currentComp.right.data.buyNowStyle.mode">
                                                        <el-radio :label="1">文字</el-radio>
                                                        <el-radio :label="2">图片</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <template v-if="currentComp.right.data.buyNowStyle.mode == 1">
                                                    <el-form-item label="文字">
                                                        <el-input v-model="currentComp.right.data.buyNowStyle.text">
                                                        </el-input>
                                                    </el-form-item>
                                                    <el-form-item label="背景1">
                                                        <d-color-picker
                                                            v-model="currentComp.right.data.buyNowStyle.color1">
                                                        </d-color-picker>
                                                    </el-form-item>
                                                    <el-form-item label="背景2">
                                                        <d-color-picker
                                                            v-model="currentComp.right.data.buyNowStyle.color2">
                                                        </d-color-picker>
                                                    </el-form-item>
                                                </template>
                                                <el-form-item v-if="currentComp.right.data.buyNowStyle.mode == 2"
                                                    label="图片">
                                                    <sa-uploader v-model="currentComp.right.data.buyNowStyle.src">
                                                    </sa-uploader>
                                                    <div class="tip">建议尺寸：56*56</div>
                                                </el-form-item>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">样式</div>
                                            <div class="wrap">
                                                <el-form-item label="上圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusTop">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="下圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusBottom">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="间距">
                                                    <d-slider v-model="currentComp.right.data.space"></d-slider>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='mplive'">
                                        <d-goods-select v-model="currentComp.right.data.mpliveList" :multiple="true"
                                            type="mplive">
                                            <template #title>
                                                直播间选择
                                            </template>
                                        </d-goods-select>
                                        <div class="card">
                                            <div class="title">直播间样式</div>
                                            <div class="wrap">
                                                <el-form-item label="选择风格">
                                                    <el-radio-group class="custom-radio-button"
                                                        v-model="currentComp.right.data.mode">
                                                        <el-radio-button :label="1">
                                                            <i class="iconfont iconmode-1"></i>
                                                        </el-radio-button>
                                                        <el-radio-button :label="2">
                                                            <i class="iconfont iconmode-2"></i>
                                                        </el-radio-button>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <template v-for="(item, field) in currentComp.right.data.goodsFields"
                                                    :key="field">
                                                    <template v-if="compNameObj[currentComp.type]?.fieldLabel[field]">
                                                        <el-form-item
                                                            :label="compNameObj[currentComp.type]?.fieldLabel[field]">
                                                            <d-color-picker v-model="item.color"
                                                                v-model:show="item.show" :isshow="true">
                                                            </d-color-picker>
                                                        </el-form-item>
                                                    </template>
                                                </template>
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="title">样式</div>
                                            <div class="wrap">
                                                <el-form-item label="上圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusTop">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="下圆角">
                                                    <d-slider v-model="currentComp.right.data.borderRadiusBottom">
                                                    </d-slider>
                                                </el-form-item>
                                                <el-form-item label="间距">
                                                    <d-slider v-model="currentComp.right.data.space"></d-slider>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="currentComp.type=='coupon'">
                                        <d-list v-model="currentComp.right.data.couponList">
                                            <template #title>优惠券选择</template>
                                            <template #listitem="{ element }">
                                                <el-form-item label-width="28px">
                                                    <div class="name sa-m-b-4">
                                                        {{ element.name }}
                                                    </div>
                                                    <div class="amount-text">
                                                        {{ element.amount_text }}
                                                    </div>
                                                </el-form-item>
                                            </template>
                                            <template #add>
                                                <el-button class="add-button" icon="Plus" @click="onSelectCoupon">添加
                                                </el-button>
                                            </template>
                                        </d-list>
                                        <div class="card">
                                            <div class="title">优惠券样式</div>
                                            <div class="wrap">
                                                <el-form-item label="选择风格">
                                                    <el-radio-group class="custom-radio-button"
                                                        v-model="currentComp.right.data.mode">
                                                        <el-radio-button :label="1">
                                                            <i class="iconfont iconmode-7"></i>
                                                        </el-radio-button>
                                                        <el-radio-button :label="2">
                                                            <i class="iconfont iconmode-8"></i>
                                                        </el-radio-button>
                                                        <el-radio-button :label="3">
                                                            <i class="iconfont iconmode-9"></i>
                                                        </el-radio-button>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item label="背景图片">
                                                    <sa-uploader v-model="currentComp.right.data.fill.bgImage">
                                                    </sa-uploader>
                                                </el-form-item>
                                                <el-form-item label="文字颜色">
                                                    <d-color-picker v-model="currentComp.right.data.fill.color">
                                                    </d-color-picker>
                                                </el-form-item>
                                                <el-form-item label="按钮背景">
                                                    <d-color-picker v-model="currentComp.right.data.button.bgColor">
                                                    </d-color-picker>
                                                </el-form-item>
                                                <el-form-item label="按钮文字">
                                                    <d-color-picker v-model="currentComp.right.data.button.color">
                                                    </d-color-picker>
                                                </el-form-item>
                                                <el-form-item label="间距">
                                                    <d-slider v-model="currentComp.right.data.space"></d-slider>
                                                </el-form-item>
                                            </div>
                                        </div>
                                    </template>
                                </template>
                                <template v-if="rightData.activeTab=='style'">
                                    <div class="card">
                                        <div class="title">组件样式</div>
                                        <div class="wrap">
                                            <template v-if="currentComp.right.style.background">
                                                <el-form-item label="组件背景">
                                                    <el-radio-group v-model="currentComp.right.style.background.type">
                                                        <el-radio label="color">纯色</el-radio>
                                                        <el-radio label="image">图片</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                                <el-form-item v-if="currentComp.right.style.background.type == 'color'"
                                                    label="选择颜色">
                                                    <d-color-picker
                                                        v-model="currentComp.right.style.background.bgColor">
                                                    </d-color-picker>
                                                </el-form-item>
                                                <el-form-item v-if="currentComp.right.style.background.type == 'image'"
                                                    label="选择图片">
                                                    <sa-uploader v-model="currentComp.right.style.background.bgImage">
                                                    </sa-uploader>
                                                </el-form-item>
                                            </template>
                                            <el-form-item
                                                v-if="currentComp.right.style.marginTop || currentComp.right.style.marginTop == 0"
                                                label="上间距">
                                                <d-slider v-model="currentComp.right.style.marginTop"></d-slider>
                                            </el-form-item>
                                            <el-form-item
                                                v-if="currentComp.right.style.marginRight || currentComp.right.style.marginRight == 0"
                                                label="右间距">
                                                <d-slider v-model="currentComp.right.style.marginRight"></d-slider>
                                            </el-form-item>
                                            <el-form-item
                                                v-if="currentComp.right.style.marginBottom || currentComp.right.style.marginBottom == 0"
                                                label="下间距">
                                                <d-slider v-model="currentComp.right.style.marginBottom"></d-slider>
                                            </el-form-item>
                                            <el-form-item
                                                v-if="currentComp.right.style.marginLeft || currentComp.right.style.marginLeft == 0"
                                                label="左间距">
                                                <d-slider v-model="currentComp.right.style.marginLeft"></d-slider>
                                            </el-form-item>
                                            <el-form-item
                                                v-if="currentComp.right.style.borderRadiusTop || currentComp.right.style.borderRadiusTop == 0"
                                                label="上圆角">
                                                <d-slider v-model="currentComp.right.style.borderRadiusTop"></d-slider>
                                            </el-form-item>
                                            <el-form-item v-if="
                                        currentComp.right.style.borderRadiusBottom || currentComp.right.style.borderRadiusBottom == 0
                                            " label="下圆角">
                                                <d-slider v-model="currentComp.right.style.borderRadiusBottom">
                                                </d-slider>
                                            </el-form-item>
                                            <el-form-item
                                                v-if="currentComp.right.style.padding || currentComp.right.style.padding == 0"
                                                label="内间距">
                                                <d-slider v-model="currentComp.right.style.padding"></d-slider>
                                            </el-form-item>
                                            <el-form-item
                                                v-if="currentComp.right.style.height || currentComp.right.style.height == 0"
                                                label="高度">
                                                <d-slider v-model="currentComp.right.style.height" :mult="6"></d-slider>
                                            </el-form-item>
                                        </div>
                                    </div>
                                </template>
                            </template>
                            <template v-if="rightData.activeTab=='css'">
                                <el-scrollbar class="cssCard">
                                    <div>{</div>
                                    <div class="pl-2 sa-flex sa-col-top"
                                        v-for="(value,key) in currentComp.right">
                                        <div class="cssKey mr-1">{{key}}:</div>
                                        <div v-if="isString(value)" class="cssValue">"{{value}}",</div>
                                        <div v-else class="cssValue">{{value}},</div>
                                    </div>
                                    <div>}</div>
                                </el-scrollbar>
                            </template>
                        </template>
                    </el-form>
                </el-scrollbar>
                <div class="right-icon sa-flex sa-row-center"
                    @click="rightData.collapsePanel = !rightData.collapsePanel">
                    <el-icon v-if="!rightData.collapsePanel">
                        <arrow-left />
                    </el-icon>
                    <el-icon v-else>
                        <arrow-right />
                    </el-icon>
                </div>
            </div>
        </el-main>
    </el-container>
    <el-dialog class="title-block-dialog" v-model="titleBlockDialog.visible">
        <el-row :gutter="16">
            <el-col :xs="12" :sm="8" :md="8" :lg="6" :xl="6" v-for="item in titleBlockDialog.titleList">
                <div class="item sa-flex sa-row-center" :class="titleBlockDialog.active?'is-active':''">
                    <img :src="item">
                    <div class="item-title sa-flex sa-row-center">标题栏</div>
                </div>
            </el-col>
        </el-row>
        <template #footer>
            <el-button type="primary" @click="onSelectTitleBlock">
                确定
            </el-button>
        </template>
    </el-dialog>
    <el-dialog class="hotzone-dialog" title="设置热区" v-model="hotzoneDialog.visible">
        <div ref="mapRef" class="mapContent" data-type="content">
            <img ref="srcRef" :src="Fast.api.cdnurl(currentComp.right.data.src)" />
            <template v-for="(item, index) in hotzoneDialog.mapList">
                <div v-if="!item.show" class="map-item sa-flex sa-row-center" :style="{
                      width: `${item.width}px`,
                      height: `${item.height}px`,
                      top: `${item.top}px`,
                      left: `${item.left}px`,
                    }" data-type="item" @mousedown="mousedown($event, index)" @mouseup="mouseup($event, index)"
                    @click.self="onSelectHotzone(index)" @dblclick="onLinkHotzone(index)">
                    {{ item.name }}
                    <div class="delete" @click.stop="onDeleteHotzone(index)">
                        <el-icon>
                            <Close />
                        </el-icon>
                    </div>
                    <div class="coor" data-type="scale"></div>
                </div>
            </template>
        </div>
        <template #footer>
            <el-button @click="onAddHotzone">添加热区</el-button>
            <el-button @click="onSaveHotzone">保存</el-button>
        </template>
    </el-dialog>
</div>