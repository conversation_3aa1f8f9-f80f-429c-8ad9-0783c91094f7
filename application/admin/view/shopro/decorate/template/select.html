{include file="/shopro/common/script" /}

<div id="select" class="template-select" v-cloak>
    <el-container class="panel-block">
        <el-main>
            <el-table height="100%" class="sa-table" :data="state.data" stripe>
                <el-table-column prop="id" label="ID" min-width="90"></el-table-column>
                <el-table-column label="名称" min-width="180">
                    <template #default="scope">
                        <div class="sa-line-1">
                            {{ scope.row.name }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="type_text" label="类型" min-width="80"></el-table-column>
                <el-table-column label="操作" min-width="100" fixed="right">
                    <template #default="scope">
                        <el-button type="primary" link @click="onSelect(scope.row)">选择</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-main>
    </el-container>
</div>