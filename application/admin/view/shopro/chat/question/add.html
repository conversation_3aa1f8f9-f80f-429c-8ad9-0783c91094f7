{include file="/shopro/common/script" /}

<div id="addEdit" class="question-form" v-cloak>
    <el-container class="panel-block">
        <el-main>
            <el-scrollbar height="100%">
                <el-form :model="form.model" :rules="form.rules" ref="formRef" label-width="100px">
                    <el-form-item label="客服分类" prop="room_id">
                        <el-cascader v-model="form.model.room_id" :options="chat.config.default_rooms" :props="{
                          label: 'name',
                          value: 'value',
                          checkStrictly: true,
                          emitPath: false,
                        }" clearable placeholder="请选择客服分类">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item label="标题" prop="title">
                        <el-input class="sa-w-360" v-model="form.model.title" placeholder="请输入标题"></el-input>
                    </el-form-item>
                    <el-form-item label="问题内容" prop="content">
                        <form role="form">
                            <textarea id="questionContent" class="editor"></textarea>
                        </form>
                    </el-form-item>
                    <el-form-item label="权重">
                        <el-input class="sa-w-360" v-model="form.model.weigh" placeholder="请输入权重" type="number">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="显示状态" prop="status">
                        <el-radio-group v-model="form.model.status">
                            <el-radio label="normal">正常</el-radio>
                            <el-radio label="hidden">隐藏</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
            </el-scrollbar>
        </el-main>
        <el-footer class="sa-footer--submit sa-flex sa-row-right">
            <el-button type="primary" @click="onConfirm">确定</el-button>
        </el-footer>
    </el-container>
</div>