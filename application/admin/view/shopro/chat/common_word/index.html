{include file="/shopro/common/script" /}

<div id="index" class="common-word-index panel panel-default panel-intro" v-cloak>
    <el-container class="panel-block">
        <el-header class="sa-header">
            <div class="sa-title sa-flex sa-row-between">
                <div class="sa-title-left">
                    <div class="left-name">常用语</div>
                    <sa-filter-condition v-model="state.filter" @filter-delete="onChangeFilter">
                    </sa-filter-condition>
                </div>
                <div class="sa-title-right">
                    <el-button class="sa-button-refresh" icon="RefreshRight" @click="getData"></el-button>
                    <el-button class="sa-button-refresh" icon="Search" @click="onOpenFilter"></el-button>
                    {if $auth->check('shopro/chat/common_word/add')}
                    <el-button icon="Plus" type="primary" @click="onAdd">添加</el-button>
                    {/if}
                </div>
            </div>
        </el-header>
        <el-main class="sa-main">
            <el-table height="100%" class="sa-table" :data="state.data" stripe @selection-change="onChangeSelection"
                @sort-change="onChangeSort">
                <el-table-column type="selection" width="48"></el-table-column>
                <el-table-column prop="id" label="ID" min-width="90" sortable="custom"> </el-table-column>
                <el-table-column label="标题" min-width="100">
                    <template #default="scope">
                        <div class="sa-table-line-1">
                            {{ scope.row.name || '-' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="客服分类" min-width="100">
                    <template #default="scope">
                        <div class="sa-table-line-1">
                            {{ scope.row.room_name || '-' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="状态" min-width="70">
                    <template #default="scope">
                        <el-tag :type="scope.row.status == 'normal' ? 'success' : 'info'">
                            {{ scope.row.status_text }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="权重" min-width="88">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{ scope.row.weigh || 0 }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="更新时间" width="172">
                    <template #default="scope">
                        {{ scope.row.updatetime || '-' }}
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" min-width="120">
                    <template #default="scope">
                        {if $auth->check('shopro/chat/common_word/edit')}
                        <el-button type="primary" link @click="onEdit(scope.row.id)">编辑</el-button>
                        {/if}
                        <el-popconfirm width="fit-content" confirm-button-text="确认" cancel-button-text="取消"
                            title="确认删除这条记录?" @confirm="onDelete(scope.row.id)">
                            <template #reference>
                                {if $auth->check('shopro/chat/common_word/delete')}
                                <el-button type="danger" link>删除</el-button>
                                {/if}
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
        </el-main>
        <el-footer class="sa-footer sa-flex sa-row-between sa-flex-wrap">
            <div class="sa-batch sa-flex">
                <div class="tip">
                    已选择 <span>{{batchHandle.data.length}}</span> 项</div>
                <div class="sa-flex">
                    {if $auth->check('shopro/chat/common_word/delete')}
                    <el-button type="danger" :disabled="!batchHandle.data.length" @click="onBatchHandle('delete')">删除
                    </el-button>
                    {/if}
                    {if $auth->check('shopro/chat/common_word/edit')}
                    <el-button type="success" :disabled="!batchHandle.data.length" @click="onBatchHandle('normal')">正常
                    </el-button>
                    {/if}
                    {if $auth->check('shopro/chat/common_word/edit')}
                    <el-button type="info" :disabled="!batchHandle.data.length" @click="onBatchHandle('hidden')">隐藏
                    </el-button>
                    {/if}
                </div>
            </div>
            <sa-pagination v-model="pagination" @pagination-change="getData"></sa-pagination>
        </el-footer>
    </el-container>
    <sa-filter v-model="state.filter" @filter-change="onChangeFilter"></sa-filter>
</div>