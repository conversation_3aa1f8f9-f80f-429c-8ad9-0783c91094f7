{include file="/shopro/common/script" /}

<style>
    .price-title {
        text-decoration: line-through;
        margin-right: 8px;
        color: var(--sa-subfont);
    }

    .live-qrcode {
        width: 150px;
        height: 150px;
    }

    .empty-data {
        width: 210px;
        height: 150px;
    }
</style>

<div id="index" class="mplive-index panel panel-default panel-intro" v-cloak>
    <el-container class="panel-block">
        <el-header class="sa-header">
            <el-tabs class="sa-tabs" v-model="dispatchType">
                <el-tab-pane label="直播间管理" name="live"></el-tab-pane>
                <el-tab-pane label="商品库管理" name="goods"></el-tab-pane>
            </el-tabs>

            <div class="sa-title sa-flex sa-row-between">
                <div class="sa-title-left">
                    <div class="left-name">{{ dispatchType === 'live' ? '直播间管理' : '商品库管理' }}</div>
                    <sa-filter-condition v-model="state.filter" @filter-delete="onChangeFilter">
                    </sa-filter-condition>
                </div>
                <div v-if="dispatchType === 'live'">
                    <el-popover placement="bottom" :width="180" trigger="click">
                        <template #reference>
                            <el-button class="sa-button-refresh">主播端</el-button>
                        </template>
                        <img class="live-qrcode" src="/assets/addons/shopro/img/live-qrcode.png" />
                    </el-popover>
                    <!-- :loading="loading"  -->
                    {if $auth->check('shopro/app/mplive/room/sync')}
                    <el-button class="sa-button-refresh" icon="RefreshRight" @click="sync">同步直播间</el-button>
                    {/if}
                    {if $auth->check('shopro/app/mplive/room/add')}
                    <el-button class="sa-button-refresh" type="primary" icon="Plus" @click="addRow">创建直播间</el-button>
                    {/if}
                </div>
                <div v-if="dispatchType === 'goods'">
                    <el-button class="sa-button-refresh" icon="RefreshRight" @click="getData()"></el-button>
                    <el-button class="sa-button-refresh" icon="Search" @click="openFilter"></el-button>
                    {if $auth->check('shopro/app/mplive/goods/add')}
                    <el-button class="sa-button-refresh" type="primary" icon="Plus" @click="addGoods">添加商品</el-button>
                    {/if}
                </div>
            </div>
        </el-header>
        <!-- v-loading="loading" -->
        <el-main class="sa-p-0">
            <el-table height="100%" class="sa-table" :data="live.data" stripe @sort-change="onChangeSort" v-if="dispatchType === 'live'">
                <el-table-column label="房间ID" sortable="custom" min-width="120" align="center">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{ scope.row.roomid }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="直播类型" min-width="120" align="center">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{ scope.row.type_text }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="直播间标题" min-width="200" align="center">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{ scope.row.name }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="主播昵称" min-width="120" align="center">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{ scope.row.anchor_name }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="状态" min-width="120" align="center">
                    <template #default="scope">
                        <div class="sa-table-line-1" :class="
                    scope.row.status === 101
                      ? 'sa-color--success'
                      : scope.row.status === 102
                      ? 'sa-color--warning'
                      : scope.row.status === 105
                      ? 'sa-color--info'
                      : 'sa-color--danger'
                  ">{{ scope.row.status_text }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="背景图" min-width="80" align="center">
                    <template #default="scope">
                        <div class="sa-flex sa-row-center">
                            <sa-image :url="scope.row.cover_img" size="30">
                            </sa-image>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="分享图" min-width="80" align="center">
                    <template #default="scope">
                        <div class="sa-flex sa-row-center">
                            <sa-image :url="scope.row.share_img" size="30"></sa-image>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="封面图" min-width="80" align="center">
                    <template #default="scope">
                        <div class="sa-flex sa-row-center">
                            <sa-image :url="scope.row.feeds_img" size="30"></sa-image>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="开播时间" min-width="200" align="center">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{
                            Moment(scope.row.start_time * 1000).format('YYYY-MM-DD HH:mm')
                            }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="结束时间" min-width="200" align="center">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{
                            Moment(scope.row.end_time * 1000).format('YYYY-MM-DD HH:mm')
                            }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="340" fixed="right" align="center">
                    <template #default="scope">
                        {if $auth->check('shopro/app/mplive/room/edit')}
                        <el-button link type="primary" @click="editRow(scope.row.roomid)">编辑</el-button>
                        {/if}
                        {if $auth->check('shopro/app/mplive/room/pushurl')}
                        <el-button link type="primary" @click="pushUrl(scope.row.roomid)"
                            v-if="scope.row.type === 1 && (scope.row.status === 101 || scope.row.status === 102)">推流地址</el-button>
                        {/if}
                        {if $auth->check('shopro/app/mplive/room/qrcode')}
                        <el-button link type="primary" @click="shareQrcode(scope.row.roomid)"
                            v-if="scope.row.status === 101 || scope.row.status === 102">分享二维码</el-button>
                        {/if}
                        {if $auth->check('shopro/app/mplive/room/playback')}
                        <el-button link type="primary" @click="playBack(scope.row.roomid)"
                            v-if="scope.row.status === 103">回放</el-button>
                        {/if}

                        <el-popconfirm width="fit-content" confirm-button-text="确认" cancel-button-text="取消"
                            title="确认删除这条记录?" @confirm="deleteApi(scope.row.roomid)">
                            <template #reference>
                                {if $auth->check('shopro/app/mplive/room/delete')}
                                <el-button link type="danger">
                                    删除
                                </el-button>
                                {/if}
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
            <el-table height="100%" class="sa-table" :data="goods.data" stripe v-if="dispatchType === 'goods'">
                <el-table-column prop="id" label="ID" min-width="80"></el-table-column>
                <el-table-column label="商品来源" min-width="120" align="center">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{ scope.row.type_text }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="商品封面" min-width="80" align="center">
                    <template #default="scope">
                        <div class="sa-flex sa-row-center">
                            <sa-image :url="scope.row.cover_img_url" size="30">
                            </sa-image>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="商品名称" min-width="220" align="center">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{ scope.row.name }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="价格形式" min-width="120" align="center">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{ scope.row.price_type_text }}</div>
                    </template>
                </el-table-column>

                <el-table-column label="价格" min-width="200" align="center">
                    <template #default="scope">
                        <div class="sa-table-line-1">
                            <div class="sa-flex sa-row-center">
                                <div :class="{ 'price-title': scope.row.price_type === 3 }">{{ scope.row.price }}元</div>
                                <div>
                                    {{ formatPrice(scope.row.price2, scope.row.price_type) }}
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="商品路径" min-width="300" align="center">
                    <template #default="scope">
                        <div class="sa-table-line-1">{{ scope.row.url }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="审核状态" min-width="120" align="center" fixed="right">
                    <template #default="scope">
                        <div class="sa-flex sa-row-center">
                            <div class="sa-table-line-1" :class="
                            scope.row.audit_status === 2
                              ? 'sa-color--success'
                              : scope.row.audit_status === 3
                              ? 'sa-color--danger' 
                              : scope.row.audit_status === 1 ? 'sa-color--warning' : 'sa-color--info'
                          ">{{ scope.row.audit_status_text }}</div>
                            <el-button link icon="RefreshRight" @click="getStatus(scope.row.id)"></el-button>
                        </div>

                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="200" fixed="right">
                    <template #default="scope">
                        {if $auth->check('shopro/app/mplive/goods/edit')}
                        <el-button link type="primary" @click="editGoods(scope.row.id)">编辑</el-button>
                        {/if}
                        {if $auth->check('shopro/app/mplive/goods/audit')}
                        <el-button link type="primary" @click="check(scope.row.id, 'resubmit')"
                            v-if="scope.row.audit_status === 0 || scope.row.audit_status === 3">提交审核</el-button>
                        {/if}
                        {if $auth->check('shopro/app/mplive/goods/audit')}
                        <el-button link type="primary" v-if="scope.row.audit_status === 1"
                            @click="check(scope.row.id, 'reset')">撤回审核</el-button>
                        {/if}
                        <el-popconfirm width="fit-content" confirm-button-text="确认" cancel-button-text="取消"
                            title="确认删除这条记录?" @confirm="deleteGoods(scope.row.id)">
                            <template #reference>
                                {if $auth->check('shopro/app/mplive/goods/delete')}
                                <el-button link type="danger">删除</el-button>
                                {/if}
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
        </el-main>
        <el-footer class="sa-footer sa-flex sa-row-between sa-flex-wrap" v-if="dispatchType === 'goods'">
            <div class="sa-batch sa-flex">
            </div>
            <sa-pagination v-model="pagination" @pagination-change="getData"></sa-pagination>
        </el-footer>
    </el-container>
    <sa-filter v-model="state.filter" @filter-change="onChangeFilter"></sa-filter>
</div>