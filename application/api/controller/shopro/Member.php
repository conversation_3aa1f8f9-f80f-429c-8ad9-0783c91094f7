<?php

namespace app\api\controller\shopro;

use app\common\controller\Api;
use app\admin\model\shopro\member\Level;
use app\admin\model\shopro\member\UserMember;
use app\admin\model\shopro\member\BenefitLog;
use app\admin\model\shopro\member\UpgradeLog;

/**
 * 会员等级API接口
 */
class Member extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    /**
     * 获取会员等级信息
     */
    public function index()
    {
        $user = $this->auth->getUser();
        
        // 获取用户会员信息
        $userMember = UserMember::getMemberByUserId($user->id);
        
        // 获取所有等级列表
        $levels = Level::getAllLevels();
        
        // 获取当前等级详情
        $currentLevel = null;
        if ($userMember->level > 0) {
            $currentLevel = Level::find($userMember->level_id);
        }
        
        // 获取下一等级信息
        $nextLevel = null;
        if ($userMember->level < 5) { // 假设最高等级是5
            $nextLevel = Level::where('consume_score', '>', $userMember->consume_score)
                             ->where('status', 'normal')
                             ->order('consume_score asc')
                             ->find();
        }
        
        $this->success('获取成功', [
            'user_member' => [
                'level' => $userMember->level,
                'level_name' => $userMember->level_name,
                'consume_score' => $userMember->consume_score,
                'total_recharge' => $userMember->total_recharge,
                'total_consume' => $userMember->total_consume,
                'progress_percent' => $userMember->progress_percent,
                'upgrade_time' => $userMember->upgrade_time,
                'status' => $userMember->status
            ],
            'current_level' => $currentLevel ? [
                'id' => $currentLevel->id,
                'name' => $currentLevel->name,
                'level' => $currentLevel->level,
                'consume_score' => $currentLevel->consume_score,
                'icon' => $currentLevel->icon,
                'color' => $currentLevel->color,
                'benefits' => $currentLevel->benefits,
                'benefits_text' => $currentLevel->benefits_text,
                'description' => $currentLevel->description
            ] : null,
            'next_level' => $nextLevel ? [
                'id' => $nextLevel->id,
                'name' => $nextLevel->name,
                'level' => $nextLevel->level,
                'consume_score' => $nextLevel->consume_score,
                'need_score' => $nextLevel->consume_score - $userMember->consume_score,
                'icon' => $nextLevel->icon,
                'color' => $nextLevel->color,
                'benefits' => $nextLevel->benefits,
                'benefits_text' => $nextLevel->benefits_text,
                'description' => $nextLevel->description
            ] : null,
            'all_levels' => $levels->toArray()
        ]);
    }

    /**
     * 获取等级列表
     */
    public function levels()
    {
        $levels = Level::getAllLevels();
        
        $this->success('获取成功', $levels->toArray());
    }

    /**
     * 获取权益使用情况
     */
    public function benefits()
    {
        $user = $this->auth->getUser();
        $month = input('month', date('Y-m'));
        
        // 获取用户会员信息
        $userMember = UserMember::getMemberByUserId($user->id);
        
        // 获取当月权益使用情况
        $benefitLogs = BenefitLog::getUserMonthlyStats($user->id, $month);
        
        // 获取当前等级权益配置
        $currentBenefits = $userMember->getCurrentBenefits();
        
        // 整理权益使用数据
        $benefitUsage = [];
        foreach ($currentBenefits as $type => $config) {
            $log = $benefitLogs->where('benefit_type', $type)->first();
            
            $benefitUsage[$type] = [
                'type' => $type,
                'name' => $this->getBenefitTypeName($type),
                'total_count' => $config['count'] ?? 0,
                'used_count' => $log ? $log->used_count : 0,
                'remaining_count' => $log ? $log->getRemainingCount() : ($config['count'] ?? 0),
                'config' => $config,
                'can_use' => $userMember->canUseBenefit($type, $month)
            ];
        }
        
        $this->success('获取成功', [
            'month' => $month,
            'level' => $userMember->level,
            'level_name' => $userMember->level_name,
            'benefits' => $benefitUsage
        ]);
    }

    /**
     * 获取升级历史
     */
    public function upgradeHistory()
    {
        $user = $this->auth->getUser();
        $page = input('page', 1);
        $limit = input('limit', 10);
        
        $logs = UpgradeLog::where('user_id', $user->id)
                         ->order('createtime desc')
                         ->paginate($limit, false, ['page' => $page]);
        
        $this->success('获取成功', [
            'list' => $logs->items(),
            'total' => $logs->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 获取会员统计信息
     */
    public function stats()
    {
        $user = $this->auth->getUser();
        
        // 获取用户会员信息
        $userMember = UserMember::getMemberByUserId($user->id);
        
        // 获取本月权益使用统计
        $month = date('Y-m');
        $benefitStats = BenefitLog::getUserMonthlyStats($user->id, $month);
        
        // 计算节省金额（这里可以根据实际业务逻辑计算）
        $savedAmount = 0;
        foreach ($benefitStats as $log) {
            if ($log->benefit_type == 'free_shipping') {
                $savedAmount += $log->used_count * 10; // 假设每次免邮节省10元
            }
        }
        
        // 获取升级历史数量
        $upgradeCount = UpgradeLog::where('user_id', $user->id)->count();
        
        $this->success('获取成功', [
            'consume_score' => $userMember->consume_score,
            'level' => $userMember->level,
            'level_name' => $userMember->level_name,
            'total_recharge' => $userMember->total_recharge,
            'total_consume' => $userMember->total_consume,
            'saved_amount' => $savedAmount,
            'upgrade_count' => $upgradeCount,
            'member_since' => date('Y-m-d', $userMember->createtime),
            'last_upgrade' => $userMember->upgrade_time ? date('Y-m-d', $userMember->upgrade_time) : null
        ]);
    }

    /**
     * 检查权益可用性
     */
    public function checkBenefit()
    {
        $user = $this->auth->getUser();
        $benefitType = input('benefit_type', '');
        $month = input('month', date('Y-m'));
        
        if (empty($benefitType)) {
            $this->error('权益类型不能为空');
        }
        
        // 获取用户会员信息
        $userMember = UserMember::getMemberByUserId($user->id);
        
        // 检查权益可用性
        $canUse = $userMember->canUseBenefit($benefitType, $month);
        $config = $userMember->getBenefitConfig($benefitType);
        $usedCount = $userMember->getMonthlyBenefitUsage($benefitType, $month);
        
        $this->success('检查完成', [
            'can_use' => $canUse,
            'benefit_type' => $benefitType,
            'config' => $config,
            'used_count' => $usedCount,
            'total_count' => $config['count'] ?? 0,
            'remaining_count' => max(0, ($config['count'] ?? 0) - $usedCount)
        ]);
    }

    /**
     * 获取权益类型名称
     */
    private function getBenefitTypeName($type)
    {
        $names = [
            'free_shipping' => '免邮权益',
            'discount' => '折扣权益',
            'lottery' => '转盘权益'
        ];
        
        return $names[$type] ?? $type;
    }

    /**
     * 模拟使用权益（测试用）
     */
    public function useBenefit()
    {
        $user = $this->auth->getUser();
        $benefitType = input('benefit_type', '');
        $orderId = input('order_id', 0);
        $remark = input('remark', '');
        
        if (empty($benefitType)) {
            $this->error('权益类型不能为空');
        }
        
        // 获取用户会员信息
        $userMember = UserMember::getMemberByUserId($user->id);
        
        // 使用权益
        $result = $userMember->useBenefit($benefitType, $orderId, $remark);
        
        if ($result) {
            $this->success('权益使用成功');
        } else {
            $this->error('权益使用失败，可能是权益不足或不可用');
        }
    }
}
