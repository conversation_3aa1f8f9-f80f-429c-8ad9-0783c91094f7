<?php

namespace app\common\library;

use app\admin\model\Memberlevel;
use app\admin\model\Usermember;
use app\admin\model\Benefitlog;
use app\admin\model\Upgradelog;
use think\Db;
use think\Log;

/**
 * 会员等级服务类
 */
class MemberService
{
    /**
     * 更新用户消费分（充值或消费时调用）
     * 
     * @param int $userId 用户ID
     * @param float $amount 金额
     * @param string $type 类型：recharge充值, consume消费
     * @param int $triggerId 触发来源ID（订单ID等）
     * @return bool
     */
    public static function updateConsumeScore($userId, $amount, $type = 'consume', $triggerId = null)
    {
        try {
            // 获取用户会员信息
            $userMember = Usermember::getMemberByUserId($userId);
            
            // 更新消费分（1元=1分）
            $score = intval($amount);
            $userMember->updateConsumeScore($score, $type, $triggerId);
            
            // 如果等级有变化，创建当月权益记录
            if ($userMember->level > 0) {
                self::createMonthlyBenefits($userId);
            }
            
            Log::info("用户{$userId}消费分更新成功，类型：{$type}，金额：{$amount}，消费分：{$score}");
            
            return true;
            
        } catch (\Exception $e) {
            Log::error("更新用户消费分失败：" . $e->getMessage());
            return false;
        }
    }

    /**
     * 创建用户当月权益记录
     * 
     * @param int $userId 用户ID
     * @param string $month 月份，默认当前月
     * @return int 创建的记录数
     */
    public static function createMonthlyBenefits($userId, $month = null)
    {
        try {
            $userMember = Usermember::getMemberByUserId($userId);
            
            if ($userMember->level == 0) {
                return 0; // 普通会员无权益
            }
            
            $benefits = $userMember->getCurrentBenefits();
            if (empty($benefits)) {
                return 0;
            }
            
            if (!$month) {
                $month = date('Y-m');
            }
            
            $expireTime = strtotime(date('Y-m-t 23:59:59', strtotime($month . '-01')));
            $logs = [];
            
            foreach ($benefits as $type => $config) {
                if (!isset($config['count']) || $config['count'] <= 0) {
                    continue;
                }
                
                // 检查是否已存在
                $exists = Benefitlog::where('user_id', $userId)
                                 ->where('benefit_type', $type)
                                 ->where('month', $month)
                                 ->find();
                
                if (!$exists) {
                    $logs[] = [
                        'user_id' => $userId,
                        'level_id' => $userMember->level_id,
                        'benefit_type' => $type,
                        'benefit_name' => self::getBenefitTypeName($type),
                        'used_count' => 0,
                        'total_count' => $config['count'],
                        'month' => $month,
                        'expire_time' => $expireTime,
                        'status' => '1',
                        'createtime' => time(),
                        'updatetime' => time()
                    ];
                }
            }
            
            if (!empty($logs)) {
                Db::name('benefitlog')->insertAll($logs);
            }
            
            return count($logs);
            
        } catch (\Exception $e) {
            Log::error("创建用户权益记录失败：" . $e->getMessage());
            return 0;
        }
    }

    /**
     * 检查并使用免邮权益
     * 
     * @param int $userId 用户ID
     * @param int $orderId 订单ID
     * @param string $shippingType 配送类型：express快递, delivery送货
     * @param float $weight 重量(KG)
     * @return array 结果信息
     */
    public static function checkAndUseFreeShipping($userId, $orderId, $shippingType = 'express', $weight = 0)
    {
        try {
            $userMember = Usermember::getMemberByUserId($userId);
            
            // 检查是否有免邮权益
            $benefits = $userMember->getCurrentBenefits();
            if (!isset($benefits['free_shipping'])) {
                return ['success' => false, 'message' => '无免邮权益'];
            }
            
            $config = $benefits['free_shipping'];
            
            // 检查重量限制
            if (isset($config['weight_limit']) && $weight > $config['weight_limit']) {
                return ['success' => false, 'message' => "超出重量限制{$config['weight_limit']}KG"];
            }
            
            // 检查配送类型限制
            if (isset($config['type']) && $config['type'] != 'all' && $config['type'] != $shippingType) {
                return ['success' => false, 'message' => '配送类型不符合权益要求'];
            }
            
            // 检查权益可用性
            $month = date('Y-m');
            $log = Benefitlog::getUserBenefitUsage($userId, 'free_shipping', $month);
            
            if (!$log) {
                // 创建权益记录
                self::createMonthlyBenefits($userId, $month);
                $log = Benefitlog::getUserBenefitUsage($userId, 'free_shipping', $month);
            }
            
            if ($log && $log->total_count != 999 && $log->used_count >= $log->total_count) {
                return ['success' => false, 'message' => '本月免邮次数已用完'];
            }
            
            // 使用权益
            if ($log) {
                $log->used_count += 1;
                $log->order_id = $orderId;
                $log->remark = "订单{$orderId}使用免邮权益";
                $log->save();
            }
            
            return ['success' => true, 'message' => '免邮权益使用成功'];
            
        } catch (\Exception $e) {
            Log::error("使用免邮权益失败：" . $e->getMessage());
            return ['success' => false, 'message' => '系统错误'];
        }
    }

    /**
     * 获取用户折扣率
     * 
     * @param int $userId 用户ID
     * @return float 折扣率（100表示无折扣，97表示97折）
     */
    public static function getUserDiscountRate($userId)
    {
        try {
            $userMember = Usermember::getMemberByUserId($userId);
            $benefits = $userMember->getCurrentBenefits();
            
            if (!isset($benefits['discount'])) {
                return 100; // 无折扣
            }
            
            return $benefits['discount']['rate'] ?? 100;
            
        } catch (\Exception $e) {
            Log::error("获取用户折扣率失败：" . $e->getMessage());
            return 100;
        }
    }

    /**
     * 获取用户转盘额外次数
     * 
     * @param int $userId 用户ID
     * @return int 额外次数
     */
    public static function getUserLotteryExtraCount($userId)
    {
        try {
            $userMember = Usermember::getMemberByUserId($userId);
            $benefits = $userMember->getCurrentBenefits();
            
            if (!isset($benefits['lottery'])) {
                return 0;
            }
            
            return $benefits['lottery']['extra_count'] ?? 0;
            
        } catch (\Exception $e) {
            Log::error("获取用户转盘额外次数失败：" . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取权益类型名称
     */
    private static function getBenefitTypeName($type)
    {
        $names = [
            'free_shipping' => '免邮权益',
            'discount' => '折扣权益',
            'lottery' => '转盘权益'
        ];
        
        return $names[$type] ?? $type;
    }

    /**
     * 批量初始化用户会员记录
     * 
     * @return int 初始化的用户数
     */
    public static function initUserMembers()
    {
        try {
            $sql = "INSERT INTO fa_usermember (user_id, level_id, level, consume_score, total_recharge, total_consume, status, createtime, updatetime)
                    SELECT 
                        id as user_id,
                        0 as level_id,
                        0 as level,
                        0 as consume_score,
                        0.00 as total_recharge,
                        0.00 as total_consume,
                        '1' as status,
                        UNIX_TIMESTAMP() as createtime,
                        UNIX_TIMESTAMP() as updatetime
                    FROM fa_user 
                    WHERE NOT EXISTS (
                        SELECT 1 FROM fa_usermember WHERE fa_usermember.user_id = fa_user.id
                    )";
            
            return Db::execute($sql);
            
        } catch (\Exception $e) {
            Log::error("批量初始化用户会员记录失败：" . $e->getMessage());
            return 0;
        }
    }

    /**
     * 重置用户权益（月度重置）
     *
     * @param int $userId 用户ID
     * @return bool
     */
    public static function resetUserBenefits($userId)
    {
        try {
            $currentMonth = date('Y-m');

            // 获取用户会员信息
            $userMember = Usermember::where('user_id', $userId)->find();
            if (!$userMember || $userMember->level <= 0) {
                return false;
            }

            // 获取当前等级的权益配置
            $benefits = $userMember->getCurrentBenefits();

            // 重置权益次数
            $freeShippingCount = $benefits['free_shipping']['count'] ?? 0;
            $lotteryCount = $benefits['lottery']['extra_count'] ?? 0;
            $discountRate = $benefits['discount']['rate'] ?? 100;

            // 更新用户权益字段
            $userMember->free_shipping_count = $freeShippingCount;
            $userMember->lottery_count = $lotteryCount;
            $userMember->discount_rate = $discountRate;
            $userMember->benefits_month = $currentMonth;
            $userMember->updatetime = time();
            $userMember->save();

            return true;

        } catch (\Exception $e) {
            Log::error("重置用户权益失败：" . $e->getMessage());
            return false;
        }
    }

    /**
     * 使用免邮权益
     *
     * @param int $userId 用户ID
     * @param int $orderId 订单ID
     * @return bool
     */
    public static function useFreeShipping($userId, $orderId = 0)
    {
        try {
            $currentMonth = date('Y-m');

            // 获取用户会员信息
            $userMember = Usermember::where('user_id', $userId)->find();
            if (!$userMember || $userMember->level <= 0) {
                return false;
            }

            // 检查权益月份，如果不是当月则重置权益
            if ($userMember->benefits_month !== $currentMonth) {
                self::resetUserBenefits($userId);
                $userMember = Usermember::where('user_id', $userId)->find(); // 重新获取
            }

            // 检查是否还有剩余次数
            if ($userMember->free_shipping_count <= 0) {
                return false;
            }

            // 使用免邮权益
            $userMember->free_shipping_count -= 1;
            $userMember->updatetime = time();
            $userMember->save();

            return true;

        } catch (\Exception $e) {
            Log::error("使用免邮权益失败：" . $e->getMessage());
            return false;
        }
    }

    /**
     * 使用抽奖权益
     *
     * @param int $userId 用户ID
     * @return bool
     */
    public static function useLottery($userId)
    {
        try {
            $currentMonth = date('Y-m');

            // 获取用户会员信息
            $userMember = Usermember::where('user_id', $userId)->find();
            if (!$userMember || $userMember->level <= 0) {
                return false;
            }

            // 检查权益月份，如果不是当月则重置权益
            if ($userMember->benefits_month !== $currentMonth) {
                self::resetUserBenefits($userId);
                $userMember = Usermember::where('user_id', $userId)->find(); // 重新获取
            }

            // 检查是否还有剩余次数
            if ($userMember->lottery_count <= 0) {
                return false;
            }

            // 使用抽奖权益
            $userMember->lottery_count -= 1;
            $userMember->updatetime = time();
            $userMember->save();

            return true;

        } catch (\Exception $e) {
            Log::error("使用抽奖权益失败：" . $e->getMessage());
            return false;
        }
    }
}
