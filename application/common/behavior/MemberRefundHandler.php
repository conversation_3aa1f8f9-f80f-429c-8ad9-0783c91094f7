<?php

namespace app\common\behavior;

use think\Log;
use think\Db;

/**
 * 会员退款处理器
 * 处理订单退款时的会员消费分扣除和免邮权益退还
 */
class MemberRefundHandler
{
    /**
     * 订单退款后处理
     * 
     * @param array $params 事件参数
     * @return void
     */
    public function orderRefundAfter($params)
    {
        try {
            $order = $params['order'];
            $items = $params['items'];
            $refundMethod = $params['refund_method'] ?? 'item_refund';
            
            Log::info("会员退款处理开始: 订单ID={$order->id}, 用户ID={$order->user_id}, 退款方式={$refundMethod}");
            
            // 开始事务处理
            Db::transaction(function () use ($order, $items, $refundMethod) {
                // 1. 处理消费分扣除
                $this->handleConsumeScoreRefund($order, $items, $refundMethod);
                
                // 2. 处理免邮权益退还
                $this->handleFreeShippingRefund($order, $refundMethod);
            });
            
            Log::info("会员退款处理完成: 订单ID={$order->id}");
            
        } catch (\Exception $e) {
            Log::error("会员退款处理失败: " . $e->getMessage());
            Log::error("错误详情: " . $e->getTraceAsString());
            // 不抛出异常，避免影响主退款流程
        }
    }
    
    /**
     * 处理消费分扣除
     * 
     * @param object $order 订单对象
     * @param array $items 退款商品列表
     * @param string $refundMethod 退款方式
     * @return void
     */
    private function handleConsumeScoreRefund($order, $items, $refundMethod)
    {
        // 获取用户会员信息
        $userMember = \app\admin\model\Usermember::where('user_id', $order->user_id)->find();
        if (!$userMember || $userMember->level <= 0) {
            Log::info("用户不是会员，跳过消费分扣除: 用户ID={$order->user_id}");
            return;
        }
        
        // 计算需要扣除的消费分
        $refundAmount = 0;
        if ($refundMethod === 'full_refund') {
            // 全额退款：扣除整个订单的消费分
            $refundAmount = $order->pay_fee;
        } else {
            // 部分退款：扣除退款商品对应的消费分
            foreach ($items as $item) {
                $refundAmount = bcadd($refundAmount, $item->refund_fee, 2);
            }
        }
        
        // 按1元=1分的比例计算需要扣除的消费分
        $scoreToDeduct = intval($refundAmount);
        
        if ($scoreToDeduct <= 0) {
            Log::info("无需扣除消费分: 退款金额={$refundAmount}");
            return;
        }
        
        // 检查用户当前消费分是否足够扣除
        if ($userMember->consume_score < $scoreToDeduct) {
            Log::warning("用户消费分不足，扣除现有全部消费分: 当前={$userMember->consume_score}, 需扣除={$scoreToDeduct}");
            $scoreToDeduct = $userMember->consume_score;
        }
        
        if ($scoreToDeduct > 0) {
            // 扣除消费分
            $oldScore = $userMember->consume_score;
            $userMember->consume_score -= $scoreToDeduct;
            $userMember->total_consume = bcsub($userMember->total_consume, $refundAmount, 2);
            $userMember->updatetime = time();
            $userMember->save();
            
            Log::info("消费分扣除成功: 用户ID={$order->user_id}, 扣除分数={$scoreToDeduct}, 原分数={$oldScore}, 现分数={$userMember->consume_score}");
            
            // 检查是否需要降级
            $this->checkLevelDowngrade($userMember, $order->id);
            
            // 记录消费分变动日志（如果有相应的日志表）
            $this->logConsumeScoreChange($order->user_id, -$scoreToDeduct, 'refund', $order->id, "订单退款扣除消费分");
        }
    }
    
    /**
     * 处理免邮权益退还
     * 
     * @param object $order 订单对象
     * @param string $refundMethod 退款方式
     * @return void
     */
    private function handleFreeShippingRefund($order, $refundMethod)
    {
        // 检查订单是否使用了免邮权益
        $orderExt = json_decode($order->ext, true);
        $usedFreeShipping = $orderExt['member_free_shipping_used'] ?? false;
        
        if (!$usedFreeShipping) {
            Log::info("订单未使用免邮权益，无需退还: 订单ID={$order->id}");
            return;
        }
        
        // 获取用户会员信息
        $userMember = \app\admin\model\Usermember::where('user_id', $order->user_id)->find();
        if (!$userMember || $userMember->level <= 0) {
            Log::info("用户不是会员，无法退还免邮权益: 用户ID={$order->user_id}");
            return;
        }
        
        // 检查权益月份
        $currentMonth = date('Y-m');
        $orderMonth = date('Y-m', $order->paid_time);
        
        if ($orderMonth !== $currentMonth) {
            Log::info("订单不是当月订单，免邮权益已过期，无法退还: 订单月份={$orderMonth}, 当前月份={$currentMonth}");
            return;
        }
        
        // 检查用户当前权益月份
        if ($userMember->benefits_month !== $currentMonth) {
            // 用户权益已重置，需要先重置权益
            \app\common\library\MemberService::resetUserBenefits($order->user_id);
            $userMember = \app\admin\model\Usermember::where('user_id', $order->user_id)->find(); // 重新获取
        }
        
        // 退还免邮次数
        $userMember->free_shipping_count += 1;
        $userMember->updatetime = time();
        $userMember->save();
        
        Log::info("免邮权益退还成功: 用户ID={$order->user_id}, 当前免邮次数={$userMember->free_shipping_count}");
        
        // 记录免邮权益变动日志
        $this->logBenefitChange($order->user_id, 'free_shipping', 1, 'refund', $order->id, "订单退款退还免邮权益");
    }
    
    /**
     * 检查等级降级
     * 
     * @param object $userMember 用户会员对象
     * @param int $orderId 订单ID
     * @return void
     */
    private function checkLevelDowngrade($userMember, $orderId)
    {
        // 获取当前消费分对应的等级
        $newLevel = \app\admin\model\Memberlevel::where('consume_score', '<=', $userMember->consume_score)
                                               ->where('status', '1')
                                               ->order('level desc')
                                               ->find();
        
        if (!$newLevel) {
            // 没有找到对应等级，降级为普通用户
            $oldLevel = $userMember->level;
            $userMember->level = 0;
            $userMember->level_id = 0;
            $userMember->upgrade_time = time();
            $userMember->save();
            
            Log::info("用户等级降级: 用户ID={$userMember->user_id}, 从VIP{$oldLevel}降级为普通用户");
            
            // 记录降级日志
            $this->logLevelChange($userMember->user_id, $oldLevel, 0, 'refund', $orderId, "退款导致等级降级");
            
        } elseif ($newLevel->level < $userMember->level) {
            // 需要降级
            $oldLevel = $userMember->level;
            $userMember->level = $newLevel->level;
            $userMember->level_id = $newLevel->id;
            $userMember->upgrade_time = time();
            $userMember->save();
            
            Log::info("用户等级降级: 用户ID={$userMember->user_id}, 从VIP{$oldLevel}降级为VIP{$newLevel->level}");
            
            // 记录降级日志
            $this->logLevelChange($userMember->user_id, $oldLevel, $newLevel->level, 'refund', $orderId, "退款导致等级降级");
            
            // 重置用户权益（因为等级变化了）
            \app\common\library\MemberService::resetUserBenefits($userMember->user_id);
        }
    }
    
    /**
     * 记录消费分变动日志
     * 
     * @param int $userId 用户ID
     * @param int $scoreChange 分数变化
     * @param string $type 变动类型
     * @param int $relatedId 关联ID
     * @param string $remark 备注
     * @return void
     */
    private function logConsumeScoreChange($userId, $scoreChange, $type, $relatedId, $remark)
    {
        try {
            // 这里可以记录到专门的积分变动日志表
            // 暂时记录到系统日志
            Log::info("消费分变动记录: 用户ID={$userId}, 变动={$scoreChange}, 类型={$type}, 关联ID={$relatedId}, 备注={$remark}");
        } catch (\Exception $e) {
            Log::error("记录消费分变动日志失败: " . $e->getMessage());
        }
    }
    
    /**
     * 记录权益变动日志
     * 
     * @param int $userId 用户ID
     * @param string $benefitType 权益类型
     * @param int $change 变动数量
     * @param string $type 变动类型
     * @param int $relatedId 关联ID
     * @param string $remark 备注
     * @return void
     */
    private function logBenefitChange($userId, $benefitType, $change, $type, $relatedId, $remark)
    {
        try {
            // 这里可以记录到专门的权益变动日志表
            // 暂时记录到系统日志
            Log::info("权益变动记录: 用户ID={$userId}, 权益类型={$benefitType}, 变动={$change}, 类型={$type}, 关联ID={$relatedId}, 备注={$remark}");
        } catch (\Exception $e) {
            Log::error("记录权益变动日志失败: " . $e->getMessage());
        }
    }
    
    /**
     * 记录等级变动日志
     * 
     * @param int $userId 用户ID
     * @param int $fromLevel 原等级
     * @param int $toLevel 新等级
     * @param string $type 变动类型
     * @param int $relatedId 关联ID
     * @param string $remark 备注
     * @return void
     */
    private function logLevelChange($userId, $fromLevel, $toLevel, $type, $relatedId, $remark)
    {
        try {
            // 记录到升级日志表
            $upgradeLog = new \app\admin\model\shopro\member\UpgradeLog();
            $upgradeLog->save([
                'user_id' => $userId,
                'from_level' => $fromLevel,
                'to_level' => $toLevel,
                'consume_score' => 0, // 降级时不记录消费分
                'trigger_type' => $type,
                'trigger_id' => $relatedId,
                'remark' => $remark
            ]);
            
            Log::info("等级变动记录成功: 用户ID={$userId}, {$fromLevel}->{$toLevel}");
        } catch (\Exception $e) {
            Log::error("记录等级变动日志失败: " . $e->getMessage());
        }
    }
}
