<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use app\admin\model\Usermember;
use app\admin\model\Benefitlog;

/**
 * 会员权益月度重置定时任务
 */
class ResetMemberBenefits extends Command
{
    protected function configure()
    {
        $this->setName('member:reset-benefits')
             ->setDescription('重置会员权益（每月1号执行）');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始执行会员权益月度重置...');
        
        try {
            $currentMonth = date('Y-m');
            $lastMonth = date('Y-m', strtotime('-1 month'));
            
            $output->writeln("当前月份: {$currentMonth}");
            $output->writeln("上个月份: {$lastMonth}");
            
            // 1. 获取所有有会员等级的用户
            $users = Usermember::where('level', '>', 0)->select();
            $output->writeln("找到 " . count($users) . " 个会员用户");
            
            $resetCount = 0;
            $errorCount = 0;
            
            foreach ($users as $user) {
                try {
                    $this->resetUserBenefits($user, $currentMonth, $output);
                    $resetCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    $output->writeln("用户 {$user->user_id} 权益重置失败: " . $e->getMessage());
                }
            }
            
            $output->writeln("权益重置完成:");
            $output->writeln("- 成功重置: {$resetCount} 个用户");
            $output->writeln("- 失败: {$errorCount} 个用户");
            
            // 2. 清理过期的权益记录（保留最近3个月）
            $this->cleanOldBenefitLogs($output);
            
        } catch (\Exception $e) {
            $output->writeln("权益重置失败: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
    
    /**
     * 重置单个用户的权益
     */
    private function resetUserBenefits($user, $currentMonth, $output)
    {
        // 获取用户当前等级的权益配置
        $benefits = $user->getCurrentBenefits();
        
        if (empty($benefits)) {
            return;
        }
        
        $output->writeln("重置用户 {$user->user_id} 的权益...");
        
        foreach ($benefits as $benefitType => $config) {
            $totalCount = $config['count'] ?? 0;
            
            if ($totalCount > 0 && $totalCount != 999) { // 999表示无限次
                // 检查当月记录是否存在
                $existingLog = Benefitlog::where([
                    'user_id' => $user->user_id,
                    'benefit_type' => $benefitType,
                    'month' => $currentMonth
                ])->find();
                
                if (!$existingLog) {
                    // 创建新的权益记录
                    $benefitLog = new Benefitlog();
                    $benefitLog->user_id = $user->user_id;
                    $benefitLog->benefit_type = $benefitType;
                    $benefitLog->month = $currentMonth;
                    $benefitLog->total_count = $totalCount;
                    $benefitLog->used_count = 0;
                    $benefitLog->remaining_count = $totalCount;
                    $benefitLog->createtime = time();
                    $benefitLog->updatetime = time();
                    $benefitLog->save();
                    
                    $output->writeln("  - {$benefitType}: 重置为 {$totalCount} 次");
                }
            }
        }
    }
    
    /**
     * 清理过期的权益记录
     */
    private function cleanOldBenefitLogs($output)
    {
        $threeMonthsAgo = date('Y-m', strtotime('-3 months'));
        
        $deletedCount = Benefitlog::where('month', '<', $threeMonthsAgo)->delete();
        
        if ($deletedCount > 0) {
            $output->writeln("清理了 {$deletedCount} 条过期权益记录（{$threeMonthsAgo} 之前）");
        }
    }
}
