# 配送范围限制修复测试

## 修复内容

### 1. 前端修改 (confirm.vue)

#### 新增功能：
1. **checkDeliveryErrors()** - 检查配送错误信息
   - 检查订单商品是否有 `dispatch_error` 字段
   - 如果有错误，显示配送范围提示信息

2. **checkDeliveryRange()** - 检查配送范围限制
   - 自提点不需要检查配送范围
   - 检查是否需要收货地址
   - 检查是否有配送错误，如果有则阻止提交订单

#### 修改的函数：
1. **getOrderInfo()** - 添加配送错误检查
   - 在订单计算完成后调用 `checkDeliveryErrors()`

2. **onConfirm()** - 添加配送范围验证
   - 在提交订单前调用 `checkDeliveryRange()`

### 2. 后端修改 (Order.php)

#### 新增功能：
1. **getPickupList()** - 获取自提点列表的别名方法
   - 兼容前端调用，实际调用 `getShangList()`

## 修复逻辑

### 问题原因：
1. 后端在计算订单时，如果收货地址不在配送范围内，不会直接报错
2. 而是将运费设为0并在商品信息中添加 `dispatch_error` 字段
3. 前端没有检查这个错误字段，导致用户可以正常提交订单

### 修复方案：
1. **计算阶段**：在订单计算完成后检查 `dispatch_error` 字段，显示提示信息
2. **提交阶段**：在提交订单前再次检查配送错误，如果有错误则阻止提交

### 用户体验：
1. 用户选择收货地址后，如果地址不在配送范围内，会立即显示提示信息
2. 用户尝试提交订单时，如果仍有配送范围问题，会弹出错误提示并阻止提交
3. 提示信息会根据当前选择的配送方式显示相应的文本（快递物流/上门配送）

## 测试场景

### 测试1：快递物流配送范围限制
1. 选择一个不在快递配送范围内的收货地址
2. 应该显示："当前收货地址不在快递物流范围内，请更换收货地址或选择其他配送方式"
3. 尝试提交订单应该被阻止

### 测试2：上门配送范围限制
1. 切换到上门配送模式
2. 选择一个不在上门配送范围内的收货地址
3. 应该显示："当前收货地址不在上门配送范围内，请更换收货地址或选择其他配送方式"
4. 尝试提交订单应该被阻止

### 测试3：自提点模式
1. 切换到自提点模式
2. 不应该有配送范围限制
3. 可以正常提交订单

### 测试4：切换配送方式
1. 在有配送范围限制的情况下
2. 切换到支持的配送方式
3. 提示信息应该消失
4. 可以正常提交订单
