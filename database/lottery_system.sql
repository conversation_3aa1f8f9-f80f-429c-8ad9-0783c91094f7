-- =====================================================
-- 转盘抽奖系统数据库表设计（简化版）
-- 符合FastAdmin规范，支持自动生成后台管理界面
-- =====================================================

-- ----------------------------
-- 抽奖奖品配置表（独立奖品管理）
-- ----------------------------
DROP TABLE IF EXISTS `fa_lotteryprize`;
CREATE TABLE `fa_lotteryprize` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '奖品名称',
  `image` varchar(255) NOT NULL DEFAULT '' COMMENT '奖品图片',
  `score_value` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '等值积分',
  `shipping_fee` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '运费',
  `probability` int(10) unsigned NOT NULL DEFAULT 100 COMMENT '中奖概率',
  `stock` int(10) NOT NULL DEFAULT -1 COMMENT '库存数量',
  `used_stock` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '已使用库存',
  `description` text COMMENT '奖品描述',
  `sort` int(10) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态:normal=正常,hidden=隐藏',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抽奖奖品配置表';

-- ----------------------------
-- 抽奖记录表
-- ----------------------------
DROP TABLE IF EXISTS `fa_lotteryrecord`;
CREATE TABLE `fa_lotteryrecord` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '用户ID',
  `prize_id` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '奖品ID',
  `prize_name` varchar(100) NOT NULL DEFAULT '' COMMENT '奖品名称',
  `prize_image` varchar(255) NOT NULL DEFAULT '' COMMENT '奖品图片',
  `score_value` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '等值积分',
  `shipping_fee` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '运费',
  `choice_type` enum('goods','score') NOT NULL DEFAULT 'goods' COMMENT '选择类型:goods=要商品,score=兑换积分',
  `delivery_status` enum('pending','processing','shipped','completed') NOT NULL DEFAULT 'pending' COMMENT '发货状态:pending=待处理,processing=处理中,shipped=已发货,completed=已完成',
  `delivery_time` bigint(16) DEFAULT NULL COMMENT '发货时间',
  `delivery_company` varchar(50) NOT NULL DEFAULT '' COMMENT '快递公司',
  `delivery_number` varchar(100) NOT NULL DEFAULT '' COMMENT '快递单号',
  `delivery_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '发货备注',
  `receiver_name` varchar(50) NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `receiver_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '收货人电话',
  `receiver_address` varchar(500) NOT NULL DEFAULT '' COMMENT '收货地址',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `prize_id` (`prize_id`),
  KEY `delivery_status` (`delivery_status`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抽奖记录表';

-- ----------------------------
-- 抽奖配置表
-- ----------------------------
DROP TABLE IF EXISTS `fa_lotteryconfig`;
CREATE TABLE `fa_lotteryconfig` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '活动标题',
  `rules` text COMMENT '活动规则',
  `background_image` varchar(255) NOT NULL DEFAULT '' COMMENT '背景图片',
  `cost_score` int(10) unsigned NOT NULL DEFAULT 100 COMMENT '每次抽奖消耗积分',
  `free_shipping_amount` decimal(10,2) NOT NULL DEFAULT 99.00 COMMENT '包邮金额',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态:normal=正常,hidden=隐藏',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抽奖配置表';

-- ----------------------------
-- 用户抽奖统计表
-- ----------------------------
DROP TABLE IF EXISTS `fa_lotteryuserstat`;
CREATE TABLE `fa_lotteryuserstat` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '用户ID',
  `date` varchar(10) NOT NULL DEFAULT '' COMMENT '统计日期',
  `total_count` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '总抽奖次数',
  `total_score_cost` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '总消耗积分',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_date` (`user_id`,`date`),
  KEY `user_id` (`user_id`),
  KEY `date` (`date`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户抽奖统计表';

-- ----------------------------
-- 插入默认奖品配置
-- ----------------------------
INSERT INTO `fa_lotteryprize` (`id`, `name`, `image`, `score_value`, `shipping_fee`, `probability`, `stock`, `used_stock`, `description`, `sort`, `status`, `createtime`, `updatetime`) VALUES
(1, '精美水杯', '/assets/img/lottery/cup.png', 500, 8.00, 2000, 100, 0, '精美保温水杯，实用又美观', 1, 'normal', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
(2, '蓝牙耳机', '/assets/img/lottery/earphone.png', 1500, 12.00, 800, 50, 0, '高品质蓝牙耳机，音质清晰', 2, 'normal', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
(3, '手机支架', '/assets/img/lottery/stand.png', 300, 5.00, 2500, 200, 0, '多功能手机支架，办公必备', 3, 'normal', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
(4, '充电宝', '/assets/img/lottery/powerbank.png', 2000, 15.00, 500, 30, 0, '大容量充电宝，出行必备', 4, 'normal', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
(5, '小夜灯', '/assets/img/lottery/light.png', 200, 6.00, 3000, 150, 0, '温馨小夜灯，营造温馨氛围', 5, 'normal', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
(6, '数据线', '/assets/img/lottery/cable.png', 100, 3.00, 3500, 300, 0, '高品质数据线，快速充电', 6, 'normal', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
(7, '谢谢参与', '/assets/img/lottery/none.png', 0, 0.00, 1700, -1, 0, '很遗憾，没有中奖，下次再来', 7, 'normal', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
(8, '神秘大奖', '/assets/img/lottery/mystery.png', 5000, 20.00, 1, 1, 0, '神秘大奖等你来拿', 8, 'normal', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000);

-- ----------------------------
-- 插入默认配置
-- ----------------------------
INSERT INTO `fa_lotteryconfig` (`id`, `title`, `rules`, `background_image`, `cost_score`, `free_shipping_amount`, `status`, `createtime`, `updatetime`) VALUES
(1, '幸运转盘', '1. 消耗积分进行抽奖\n2. 中奖后可选择要商品或兑换积分\n3. 选择商品需支付运费（满99元包邮）', '/assets/img/lottery/bg.jpg', 100, 99.00, 'normal', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000);

-- =====================================================
-- FastAdmin字段注释说明：
-- 
-- 1. enum字段格式：enum('value1','value2') COMMENT '字段说明:value1=显示名1,value2=显示名2'
-- 2. 关联字段格式：int(10) unsigned COMMENT '关联表名ID'
-- 3. 图片字段格式：varchar(255) COMMENT '图片路径'
-- 4. 时间字段格式：bigint(16) COMMENT '时间戳(毫秒)'
-- 5. JSON字段格式：text COMMENT 'JSON配置'
-- 6. 状态字段格式：enum('normal','hidden') COMMENT '状态:normal=正常,hidden=隐藏'
-- 7. 金额字段格式：decimal(10,2) COMMENT '金额'
-- 
-- 这些注释格式将被FastAdmin命令行工具识别，自动生成对应的：
-- - 表单控件（下拉框、单选框、复选框、图片上传、时间选择器、金额输入等）
-- - 列表显示格式
-- - 搜索条件
-- - 数据验证规则
-- =====================================================