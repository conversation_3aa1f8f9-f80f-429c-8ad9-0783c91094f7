<?php
/**
 * 会员等级系统安装脚本
 * 
 * 使用方法：
 * php install_member_system.php
 */

// 数据库配置
$config = [
    'host' => '**************',
    'database' => 'mi.xiniu.wedomi.cn',
    'username' => 'mi.xiniu.wedomi.cn',
    'password' => 'je6ncJREMDhxZnXG',
    'charset' => 'utf8mb4'
];

try {
    echo "开始安装会员等级系统...\n\n";
    
    // 连接数据库
    $pdo = new PDO(
        "mysql:host={$config['host']};dbname={$config['database']};charset={$config['charset']}", 
        $config['username'], 
        $config['password']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功\n";
    
    // 读取SQL文件
    $sqlFile = 'member_level_system.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: {$sqlFile}");
    }
    
    $sql = file_get_contents($sqlFile);
    echo "✅ SQL文件读取成功\n";
    
    // 执行SQL语句
    $statements = explode(';', $sql);
    $executedCount = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $executedCount++;
        } catch (PDOException $e) {
            // 忽略表已存在的错误
            if (strpos($e->getMessage(), 'already exists') === false) {
                echo "⚠️ SQL执行警告: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "✅ SQL语句执行完成，共执行 {$executedCount} 条语句\n\n";
    
    // 验证表是否创建成功
    $tables = [
        'fa_shopro_member_level' => '会员等级配置表',
        'fa_shopro_user_member' => '用户会员等级记录表',
        'fa_shopro_member_benefit_log' => '会员权益使用记录表',
        'fa_shopro_member_upgrade_log' => '会员等级升级记录表'
    ];
    
    echo "📋 验证数据表创建情况：\n";
    foreach ($tables as $table => $desc) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->rowCount() > 0) {
            echo "  ✅ {$desc} ({$table})\n";
        } else {
            echo "  ❌ {$desc} ({$table}) - 创建失败\n";
        }
    }
    
    // 验证默认数据
    echo "\n📊 验证默认数据：\n";
    
    // 检查等级配置
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM fa_shopro_member_level");
    $levelCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "  会员等级配置: {$levelCount} 个等级\n";
    
    // 检查用户会员记录
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM fa_shopro_user_member");
    $memberCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "  用户会员记录: {$memberCount} 个用户\n";
    
    // 显示等级配置详情
    echo "\n🎯 等级配置详情：\n";
    $stmt = $pdo->query("SELECT name, level, consume_score, benefits FROM fa_shopro_member_level ORDER BY level");
    $levels = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($levels as $level) {
        $benefits = json_decode($level['benefits'], true);
        $benefitText = [];
        
        if (isset($benefits['free_shipping'])) {
            $shipping = $benefits['free_shipping'];
            $count = $shipping['count'] == 999 ? '无限次' : $shipping['count'] . '次/月';
            $benefitText[] = "免{$shipping['weight_limit']}KG快递费{$count}";
        }
        
        if (isset($benefits['discount']) && $benefits['discount']['rate'] < 100) {
            $benefitText[] = "全单{$benefits['discount']['rate']}折";
        }
        
        if (isset($benefits['lottery'])) {
            $benefitText[] = "转盘+{$benefits['lottery']['extra_count']}次/月";
        }
        
        echo "  {$level['name']}: 需要{$level['consume_score']}消费分 - " . implode('，', $benefitText) . "\n";
    }
    
    // 创建测试数据（可选）
    echo "\n🧪 是否创建测试数据？(y/n): ";
    $handle = fopen("php://stdin", "r");
    $input = trim(fgets($handle));
    fclose($handle);
    
    if (strtolower($input) === 'y') {
        echo "创建测试数据...\n";
        
        // 为前3个用户创建测试会员数据
        $testUsers = [
            ['user_id' => 1, 'consume_score' => 1500, 'level' => 1],
            ['user_id' => 2, 'consume_score' => 5000, 'level' => 2],
            ['user_id' => 3, 'consume_score' => 10000, 'level' => 3]
        ];
        
        foreach ($testUsers as $user) {
            // 检查用户是否存在
            $stmt = $pdo->prepare("SELECT id FROM fa_user WHERE id = ?");
            $stmt->execute([$user['user_id']]);
            
            if ($stmt->rowCount() > 0) {
                // 更新用户会员信息
                $stmt = $pdo->prepare("
                    UPDATE fa_shopro_user_member 
                    SET consume_score = ?, level = ?, level_id = ?, upgrade_time = UNIX_TIMESTAMP()
                    WHERE user_id = ?
                ");
                $stmt->execute([$user['consume_score'], $user['level'], $user['level'], $user['user_id']]);
                
                echo "  ✅ 用户{$user['user_id']}测试数据创建成功\n";
            }
        }
    }
    
    echo "\n🎉 会员等级系统安装完成！\n\n";
    
    echo "📝 后续步骤：\n";
    echo "1. 在个人中心添加会员等级入口\n";
    echo "2. 在订单流程中集成会员权益逻辑\n";
    echo "3. 在充值/消费时调用 MemberService::updateConsumeScore() 更新消费分\n";
    echo "4. 在下单时调用 MemberService::checkAndUseFreeShipping() 检查免邮权益\n";
    echo "5. 在计算订单金额时调用 MemberService::getUserDiscountRate() 获取折扣\n\n";
    
    echo "🔧 API接口地址：\n";
    echo "- 会员信息: GET /api/shopro/member/index\n";
    echo "- 权益使用: GET /api/shopro/member/benefits\n";
    echo "- 升级历史: GET /api/shopro/member/upgradeHistory\n";
    echo "- 会员统计: GET /api/shopro/member/stats\n\n";
    
    echo "📱 前端页面：\n";
    echo "- 会员等级页面: /pages/member/level\n\n";
    
} catch (Exception $e) {
    echo "❌ 安装失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>
