<?php
// 查看最新的日志文件
try {
    echo "=== 查看最新日志 ===\n\n";
    
    $logDir = 'runtime/log/202507';
    $today = date('d');
    $logFile = $logDir . '/' . $today . '.log';
    
    echo "日志文件: {$logFile}\n\n";
    
    if (file_exists($logFile)) {
        echo "文件存在，读取最新内容...\n\n";
        
        // 读取文件内容
        $content = file_get_contents($logFile);
        
        // 按行分割
        $lines = explode("\n", $content);
        
        // 只显示最后100行
        $recentLines = array_slice($lines, -100);
        
        // 过滤包含免邮相关的日志
        $filteredLines = [];
        foreach ($recentLines as $line) {
            if (strpos($line, '免邮') !== false ||
                strpos($line, 'shipping') !== false ||
                strpos($line, '订单金额计算') !== false ||
                strpos($line, '会员') !== false ||
                strpos($line, 'dispatch_amount') !== false ||
                strpos($line, 'pay_fee') !== false ||
                strpos($line, 'goods_amount') !== false ||
                strpos($line, 'order_amount') !== false ||
                strpos($line, 'total_discount') !== false ||
                strpos($line, '计算公式') !== false) {
                $filteredLines[] = $line;
            }
        }
        
        if (!empty($filteredLines)) {
            echo "找到相关日志 " . count($filteredLines) . " 条:\n";
            echo str_repeat("-", 80) . "\n";
            foreach ($filteredLines as $line) {
                echo $line . "\n";
            }
            echo str_repeat("-", 80) . "\n";
        } else {
            echo "未找到相关日志，显示最后20行:\n";
            echo str_repeat("-", 80) . "\n";
            $lastLines = array_slice($lines, -20);
            foreach ($lastLines as $line) {
                if (trim($line)) {
                    echo $line . "\n";
                }
            }
            echo str_repeat("-", 80) . "\n";
        }
        
    } else {
        echo "日志文件不存在\n";
        
        // 列出日志目录中的文件
        if (is_dir($logDir)) {
            echo "\n日志目录中的文件:\n";
            $files = scandir($logDir);
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    echo "- {$file}\n";
                }
            }
        }
    }
    
    echo "\n使用说明:\n";
    echo "1. 在小程序中进入订单确认页面\n";
    echo "2. 触发订单计算接口\n";
    echo "3. 重新运行此脚本查看日志\n";
    echo "4. 查找包含 '免邮权益' 的日志行\n";
    
} catch (Exception $e) {
    echo "❌ 查看日志失败: " . $e->getMessage() . "\n";
}
?>
