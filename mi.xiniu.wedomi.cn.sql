/*
 Navicat Premium Dump SQL

 Source Server         : 喜田
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : **************:3306
 Source Schema         : mi.xiniu.wedomi.cn

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 17/07/2025 16:38:51
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for fa_admin
-- ----------------------------
DROP TABLE IF EXISTS `fa_admin`;
CREATE TABLE `fa_admin`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户名',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '昵称',
  `password` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码',
  `salt` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码盐',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '电子邮箱',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `loginfailure` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '失败次数',
  `logintime` bigint(16) NULL DEFAULT NULL COMMENT '登录时间',
  `loginip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录IP',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `token` varchar(59) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'Session标识',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '管理员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_admin_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_admin_log`;
CREATE TABLE `fa_admin_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `username` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '管理员名字',
  `url` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作页面',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '日志标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'IP',
  `useragent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'User-Agent',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `name`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 454 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '管理员日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_area
-- ----------------------------
DROP TABLE IF EXISTS `fa_area`;
CREATE TABLE `fa_area`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `pid` int(10) NULL DEFAULT NULL COMMENT '父id',
  `shortname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '简称',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `mergename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '全称',
  `level` tinyint(4) NULL DEFAULT NULL COMMENT '层级:1=省,2=市,3=区/县',
  `pinyin` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拼音',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '长途区号',
  `zip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮编',
  `first` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '首字母',
  `lng` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经度',
  `lat` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纬度',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3749 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '地区表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_attachment
-- ----------------------------
DROP TABLE IF EXISTS `fa_attachment`;
CREATE TABLE `fa_attachment`  (
  `id` int(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '类别',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '物理路径',
  `imagewidth` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '宽度',
  `imageheight` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '高度',
  `imagetype` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图片类型',
  `imageframes` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '图片帧数',
  `filename` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '文件名称',
  `filesize` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文件大小',
  `mimetype` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'mime类型',
  `extparam` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '透传数据',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建日期',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `uploadtime` bigint(16) NULL DEFAULT NULL COMMENT '上传时间',
  `storage` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'local' COMMENT '存储位置',
  `sha1` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '文件 sha1编码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附件表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_auth_group
-- ----------------------------
DROP TABLE IF EXISTS `fa_auth_group`;
CREATE TABLE `fa_auth_group`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父组别',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '组名',
  `rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则ID',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分组表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_auth_group_access
-- ----------------------------
DROP TABLE IF EXISTS `fa_auth_group_access`;
CREATE TABLE `fa_auth_group_access`  (
  `uid` int(10) UNSIGNED NOT NULL COMMENT '会员ID',
  `group_id` int(10) UNSIGNED NOT NULL COMMENT '级别ID',
  UNIQUE INDEX `uid_group_id`(`uid`, `group_id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE,
  INDEX `group_id`(`group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '权限分组表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_auth_rule
-- ----------------------------
DROP TABLE IF EXISTS `fa_auth_rule`;
CREATE TABLE `fa_auth_rule`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` enum('menu','file') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'file' COMMENT 'menu为菜单,file为权限节点',
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规则名称',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规则名称',
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图标',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规则URL',
  `condition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '条件',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `ismenu` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为菜单',
  `menutype` enum('addtabs','blank','dialog','ajax') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单类型',
  `extend` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '扩展属性',
  `py` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '拼音首字母',
  `pinyin` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '拼音',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE,
  INDEX `weigh`(`weigh`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 455 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '节点表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_category
-- ----------------------------
DROP TABLE IF EXISTS `fa_category`;
CREATE TABLE `fa_category`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父ID',
  `type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '栏目类型',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `flag` set('hot','index','recommend') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图片',
  `keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '关键字',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '描述',
  `diyname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '自定义名称',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `weigh`(`weigh`, `id`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_command
-- ----------------------------
DROP TABLE IF EXISTS `fa_command`;
CREATE TABLE `fa_command`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '类型',
  `params` varchar(1500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '参数',
  `command` varchar(1500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '命令',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '返回结果',
  `executetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '执行时间',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `status` enum('successed','failured') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'failured' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '在线命令表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_config
-- ----------------------------
DROP TABLE IF EXISTS `fa_config`;
CREATE TABLE `fa_config`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '变量名',
  `group` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '分组',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '变量标题',
  `tip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '变量描述',
  `type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '类型:string,text,int,bool,array,datetime,date,file',
  `visible` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '可见条件',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '变量值',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '变量字典数据',
  `rule` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证规则',
  `extend` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '扩展属性',
  `setting` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '配置',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_ems
-- ----------------------------
DROP TABLE IF EXISTS `fa_ems`;
CREATE TABLE `fa_ems`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `event` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '事件',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '邮箱',
  `code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证码',
  `times` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '验证次数',
  `ip` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'IP',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '邮箱验证码表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_jobs
-- ----------------------------
DROP TABLE IF EXISTS `fa_jobs`;
CREATE TABLE `fa_jobs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED NULL DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '队列' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_pickup
-- ----------------------------
DROP TABLE IF EXISTS `fa_pickup`;
CREATE TABLE `fa_pickup`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提货点名称',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提货点电话',
  `address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提货点地址',
  `business_hours` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '营业时间',
  `longitude` decimal(10, 7) NULL DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10, 7) NULL DEFAULT NULL COMMENT '纬度',
  `status` enum('up','down') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'up' COMMENT '状态:up=启用,down=禁用',
  `weigh` int(8) NOT NULL DEFAULT 0 COMMENT '权重',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '提货点信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_activity
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_activity`;
CREATE TABLE `fa_shopro_activity`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动名称',
  `classify` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动类目',
  `type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动类别',
  `goods_ids` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品组',
  `prehead_time` bigint(16) NULL DEFAULT NULL COMMENT '预热时间',
  `start_time` bigint(16) NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` bigint(16) NULL DEFAULT NULL COMMENT '结束时间',
  `rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '规则',
  `richtext_id` int(11) NULL DEFAULT NULL COMMENT '活动说明',
  `richtext_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明标题',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '营销活动' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_activity_gift_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_activity_gift_log`;
CREATE TABLE `fa_shopro_activity_gift_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `type` enum('coupon','score','money','goods') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '礼品类型:coupon=优惠券,score=积分,money=余额,goods=商品',
  `gift` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '礼品',
  `value` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '价值',
  `rules` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则',
  `status` enum('waiting','finish','fail') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'waiting' COMMENT '状态:waiting=等待赠送,finish=赠送完成,fail=赠送失败',
  `fail_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '赠送失败原因',
  `errors` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '具体原因',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '满赠记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_activity_groupon
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_activity_groupon`;
CREATE TABLE `fa_shopro_activity_groupon`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '团长',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `activity_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动',
  `num` int(10) NOT NULL DEFAULT 0 COMMENT '成团人数',
  `current_num` int(10) NOT NULL DEFAULT 0 COMMENT '当前人数',
  `status` enum('invalid','ing','finish','finish_fictitious') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'ing' COMMENT '状态:invalid=已过期,ing=进行中,finish=已成团,finish_fictitious=虚拟成团',
  `expire_time` bigint(16) NULL DEFAULT NULL COMMENT '过期时间',
  `finish_time` bigint(16) NULL DEFAULT NULL COMMENT '成团时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '拼团' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_activity_groupon_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_activity_groupon_log`;
CREATE TABLE `fa_shopro_activity_groupon_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `groupon_id` int(11) NOT NULL DEFAULT 0 COMMENT '团',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `goods_sku_price_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品规格',
  `activity_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动',
  `is_leader` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否团长:0=不是,1=是',
  `is_fictitious` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否虚拟:0=不是,1=是',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `is_refund` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否退款:0=不是,1=是',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `groupon_id`(`groupon_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '参团记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_activity_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_activity_order`;
CREATE TABLE `fa_shopro_activity_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `activity_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动',
  `activity_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动标题',
  `activity_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动类型',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `pay_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '金额',
  `discount_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额/赠送金额',
  `goods_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '参与商品金额',
  `goods_ids` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参与商品',
  `status` enum('unpaid','paid') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'unpaid' COMMENT '状态:unpaid=未支付,paid=已支付',
  `ext` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附加信息',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `activity_id`(`activity_id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '活动订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_activity_signin
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_activity_signin`;
CREATE TABLE `fa_shopro_activity_signin`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `activity_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动',
  `date` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '签到日期',
  `score` int(10) NOT NULL DEFAULT 0 COMMENT '所得积分',
  `is_replenish` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否补签:0=正常,1=补签',
  `rules` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '活动签到' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_activity_sku_price
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_activity_sku_price`;
CREATE TABLE `fa_shopro_activity_sku_price`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动',
  `goods_sku_price_id` int(11) NOT NULL DEFAULT 0 COMMENT '规格',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `stock` int(10) NOT NULL DEFAULT 0 COMMENT '库存',
  `sales` int(10) NOT NULL DEFAULT 0 COMMENT '销量',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `ext` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附加字段',
  `status` enum('up','down') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'up' COMMENT '规格状态:up=上架,down=下架',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '活动规格价格' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_cart
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_cart`;
CREATE TABLE `fa_shopro_cart`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `goods_sku_price_id` int(11) NOT NULL DEFAULT 0 COMMENT '规格',
  `goods_num` int(10) NOT NULL DEFAULT 0 COMMENT '数量',
  `snapshot_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '快照价格',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `goods_id`(`goods_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `goods_sku_price_id`(`goods_sku_price_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '购物车' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_category
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_category`;
CREATE TABLE `fa_shopro_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名称',
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属分类',
  `style` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '样式',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '状态:normal=正常,hidden=隐藏',
  `weigh` int(8) NOT NULL DEFAULT 0 COMMENT '权重',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 186 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_chat_common_word
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_chat_common_word`;
CREATE TABLE `fa_shopro_chat_common_word`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'admin' COMMENT '房间号',
  `name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '状态',
  `weigh` int(8) NOT NULL DEFAULT 0 COMMENT '权重',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `room_id`(`room_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '常用语' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_chat_customer_service
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_chat_customer_service`;
CREATE TABLE `fa_shopro_chat_customer_service`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客服昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客服头像',
  `room_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'admin' COMMENT '客服房间',
  `max_num` int(10) NOT NULL DEFAULT 10 COMMENT '最大接待人数',
  `last_time` bigint(16) NULL DEFAULT NULL COMMENT '上次服务时间',
  `status` enum('offline','online','busy') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'offline' COMMENT '状态',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'chat客服' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_chat_customer_service_user
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_chat_customer_service_user`;
CREATE TABLE `fa_shopro_chat_customer_service_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_service_id` int(11) NOT NULL DEFAULT 0 COMMENT '客服',
  `auth` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '认证类型',
  `auth_id` int(11) NOT NULL DEFAULT 0 COMMENT '认证用户',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `customer_service_id`(`customer_service_id`) USING BTREE,
  INDEX `auth`(`auth`, `auth_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客服用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_chat_question
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_chat_question`;
CREATE TABLE `fa_shopro_chat_question`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `room_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'admin' COMMENT '房间号',
  `title` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '问题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '状态',
  `weigh` int(8) NOT NULL DEFAULT 0 COMMENT '权重',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `room_id`(`room_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '猜你想问' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_chat_record
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_chat_record`;
CREATE TABLE `fa_shopro_chat_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chat_user_id` int(11) NOT NULL DEFAULT 0 COMMENT '顾客',
  `room_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'admin' COMMENT '房间号',
  `sender_identify` enum('customer_service','customer') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'customer' COMMENT '发送身份',
  `sender_id` int(11) NOT NULL DEFAULT 0 COMMENT '发送者',
  `message_type` enum('text','image','file','system','goods','order') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'text' COMMENT '消息类型',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '消息',
  `read_time` bigint(16) NULL DEFAULT NULL COMMENT '读取时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `chat_user_id`(`chat_user_id`) USING BTREE,
  INDEX `sender_identify`(`sender_identify`, `sender_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '聊天记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_chat_service_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_chat_service_log`;
CREATE TABLE `fa_shopro_chat_service_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chat_user_id` int(11) NOT NULL DEFAULT 0 COMMENT 'chat user',
  `customer_service_id` int(11) NOT NULL DEFAULT 0 COMMENT '客服',
  `room_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'admin' COMMENT '房间号',
  `starttime` bigint(16) NULL DEFAULT NULL COMMENT '开始时间',
  `endtime` bigint(16) NULL DEFAULT NULL COMMENT '结束时间',
  `status` enum('waiting','ing','end') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'waiting' COMMENT '状态',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `chat_user_id`(`chat_user_id`) USING BTREE,
  INDEX `customer_service_id`(`customer_service_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'chat服务记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_chat_user
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_chat_user`;
CREATE TABLE `fa_shopro_chat_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标示',
  `auth` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '认证类型',
  `auth_id` int(11) NOT NULL DEFAULT 0 COMMENT '认证用户',
  `nickname` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `customer_service_id` int(11) NOT NULL DEFAULT 0 COMMENT '最后接待客服',
  `last_time` bigint(16) NULL DEFAULT NULL COMMENT '上次在线时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `session_id`(`session_id`) USING BTREE,
  INDEX `auth`(`auth`, `auth_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'chat用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_commission_agent
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_commission_agent`;
CREATE TABLE `fa_shopro_commission_agent`  (
  `user_id` int(11) NOT NULL COMMENT '用户',
  `level` int(11) NOT NULL COMMENT '分销商等级',
  `apply_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '申请信息',
  `total_income` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总收益',
  `child_order_money_0` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '自购/直推分销订单金额',
  `child_order_money_1` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '一级分销订单总金额',
  `child_order_money_2` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '二级分销订单总金额',
  `child_order_money_all` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '团队分销订单总金额',
  `child_order_count_0` int(11) NOT NULL DEFAULT 0 COMMENT '自购/直推分销订单数量',
  `child_order_count_1` int(11) NOT NULL DEFAULT 0 COMMENT '一级分销订单数量',
  `child_order_count_2` int(11) NOT NULL DEFAULT 0 COMMENT '二级分销订单数量',
  `child_order_count_all` int(11) NOT NULL DEFAULT 0 COMMENT '团队分销订单数量',
  `child_agent_count_1` int(11) NOT NULL DEFAULT 0 COMMENT '直推分销商人数',
  `child_agent_count_2` int(11) NOT NULL DEFAULT 0 COMMENT '二级分销商人数',
  `child_agent_count_all` int(11) NOT NULL DEFAULT 0 COMMENT '团队分销商人数',
  `child_agent_level_1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '一级分销商等级统计',
  `child_agent_level_all` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '团队分销商等级统计',
  `child_user_count_1` int(11) NOT NULL DEFAULT 0 COMMENT '一级用户人数',
  `child_user_count_2` int(11) NOT NULL DEFAULT 0 COMMENT '二级用户人数',
  `child_user_count_all` int(11) NOT NULL DEFAULT 0 COMMENT '团队用户人数',
  `upgrade_lock` tinyint(4) NOT NULL DEFAULT 0 COMMENT '升级锁定:0=不锁定,1=锁定',
  `apply_num` int(11) NOT NULL DEFAULT 0 COMMENT '提交申请次数',
  `level_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '升级状态:0=不升级,>1=待升级等级',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '分销商状态:forbidden=禁用,pending=审核中,freeze=冻结,normal=正常,reject=拒绝',
  `become_time` bigint(16) NULL DEFAULT NULL COMMENT '成为分销商时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销商' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_commission_goods
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_commission_goods`;
CREATE TABLE `fa_shopro_commission_goods`  (
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '分销商品',
  `self_rules` tinyint(4) NULL DEFAULT 0 COMMENT '独立设置佣金:0=否,1=是',
  `commission_rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '佣金设置',
  `status` tinyint(4) NOT NULL COMMENT '状态:0=不参与分销,1=参与分销',
  `commission_config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '独立佣金规则',
  `commission_order_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否计入业绩:0=否,1=是',
  PRIMARY KEY (`goods_id`) USING BTREE,
  UNIQUE INDEX `goods_id`(`goods_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销商品' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_commission_level
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_commission_level`;
CREATE TABLE `fa_shopro_commission_level`  (
  `level` int(11) NOT NULL COMMENT '权重等级',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '等级名称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '等级徽章',
  `commission_rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '佣金比例设置',
  `upgrade_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '升级方式',
  `upgrade_rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '升级规则',
  PRIMARY KEY (`level`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销商等级' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_commission_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_commission_log`;
CREATE TABLE `fa_shopro_commission_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL DEFAULT 0 COMMENT '分销商',
  `event` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事件标识:agent=分销商日志,level=等级变动日志,order=分销业绩,team=团队日志,reward=佣金日志,share=分享日志',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `oper_type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人:admin=管理员,system=系统,user=用户',
  `oper_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人ID',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `agent_id`(`agent_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销动态日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_commission_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_commission_order`;
CREATE TABLE `fa_shopro_commission_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `self_buy` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否分销内购:0=分销订单,1=内购订单',
  `order_item_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单商品',
  `buyer_id` int(11) NOT NULL DEFAULT 0 COMMENT '购买人',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `agent_id` int(11) NOT NULL DEFAULT 0 COMMENT '分销商',
  `amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品结算金额',
  `reward_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品结算方式',
  `reward_event` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '佣金结算事件',
  `commission_order_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '分销商业绩:-2=已扣除,-1=已取消,0=不计入,1=已计入',
  `commission_reward_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '佣金处理状态:-2=已退回,-1=已取消,0=未结算,1=已结算',
  `commission_rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '执行佣金结算规则',
  `commission_time` bigint(16) NULL DEFAULT NULL COMMENT '结算时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `order_item_id`(`order_item_id`) USING BTREE,
  INDEX `buyer_id`(`buyer_id`) USING BTREE,
  INDEX `goods_id`(`goods_id`) USING BTREE,
  INDEX `agent_id`(`agent_id`) USING BTREE,
  INDEX `createtime`(`createtime`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_commission_reward
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_commission_reward`;
CREATE TABLE `fa_shopro_commission_reward`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL DEFAULT 0 COMMENT '分销商',
  `buyer_id` int(11) NOT NULL DEFAULT 0 COMMENT '购买人',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `order_item_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单商品',
  `commission_order_id` int(11) NOT NULL DEFAULT 0 COMMENT '分销订单',
  `type` enum('money','score','change','bank','commission') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'commission' COMMENT '打款方式:commission=佣金钱包,money=余额钱包,score=积分,cash=现金(手动打款),change=企业付款到零钱,bank=企业付款到银行卡',
  `commission` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '佣金',
  `original_commission` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '原始佣金',
  `commission_level` tinyint(4) NOT NULL DEFAULT 0 COMMENT '执行层级',
  `agent_level` int(10) NOT NULL DEFAULT 0 COMMENT '执行等级',
  `commission_rules` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行规则',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态:-2=已退回,-1=已取消,0=待入账,1=已入账',
  `commission_time` bigint(16) NULL DEFAULT NULL COMMENT '结算时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `commission_order_id`(`commission_order_id`) USING BTREE,
  INDEX `agent_id`(`agent_id`) USING BTREE,
  INDEX `buyer_id`(`buyer_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分销佣金' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_config
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_config`;
CREATE TABLE `fa_shopro_config`  (
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置标识',
  `parent_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上级标识',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型:group,string,text,int,radio,select,select_mult,bool,array,datetime,date,file',
  `value` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置内容',
  `store_range` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置选项',
  `rule` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '验证规则',
  `weigh` int(8) NOT NULL DEFAULT 50 COMMENT '权重',
  PRIMARY KEY (`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_coupon
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_coupon`;
CREATE TABLE `fa_shopro_coupon`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `type` enum('reduce','discount') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'reduce' COMMENT '类型:reduce=满减券,discount=折扣券',
  `use_scope` enum('all_use','goods','disabled_goods','category') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'all_use' COMMENT '可用范围:all_use=全场通用,goods=指定商品可用,disabled_goods=指定商品不可用,category=指定分类可用',
  `items` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '可用范围值',
  `amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '券面额',
  `max_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大抵扣(折扣券)',
  `enough` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '消费门槛',
  `stock` int(10) NOT NULL DEFAULT 0 COMMENT '库存',
  `limit_num` int(10) NOT NULL DEFAULT 0 COMMENT '每人限领',
  `get_start_time` bigint(16) NOT NULL DEFAULT 0 COMMENT '领取开始时间',
  `get_end_time` bigint(16) NOT NULL DEFAULT 0 COMMENT '领取结束时间',
  `use_time_type` enum('range','days') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'range' COMMENT '使用时间类型:range=固定区间,days=相对天数',
  `use_start_time` bigint(16) NOT NULL DEFAULT 0 COMMENT '使用开始时间',
  `use_end_time` bigint(16) NOT NULL DEFAULT 0 COMMENT '使用结束时间',
  `start_days` int(10) NOT NULL DEFAULT 0 COMMENT '开始有效天数',
  `days` int(10) NOT NULL DEFAULT 0 COMMENT '有效天数',
  `is_double_discount` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠叠加:0=不可叠加,1=可叠加',
  `description` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `status` enum('normal','hidden','disabled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '状态:normal=公开,hidden=后台发放,disabled=禁用',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_data_area
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_data_area`;
CREATE TABLE `fa_shopro_data_area`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '上级',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '行政区名称',
  `level` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '行政区级别',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 713207 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '地区' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_data_express
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_data_express`;
CREATE TABLE `fa_shopro_data_express`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '快递公司',
  `code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司编号',
  `weigh` int(8) NOT NULL DEFAULT 0 COMMENT '权重',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `code`(`code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 308 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '快递公司' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_data_fake_user
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_data_fake_user`;
CREATE TABLE `fa_shopro_data_fake_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `nickname` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `password` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `gender` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '性别',
  `email` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '虚拟用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_data_faq
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_data_faq`;
CREATE TABLE `fa_shopro_data_faq`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '状态:normal=显示,hidden=隐藏',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '常见问题' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_data_page
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_data_page`;
CREATE TABLE `fa_shopro_data_page`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '路径',
  `group` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分组',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 50 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '前端路由' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_data_richtext
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_data_richtext`;
CREATE TABLE `fa_shopro_data_richtext`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '富文本' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_decorate
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_decorate`;
CREATE TABLE `fa_shopro_decorate`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `type` enum('template','diypage','designer') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'template' COMMENT '模板类型:template=店铺模板,diypage=自定义页面,designer=设计师模板',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `platform` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支持平台',
  `status` enum('enable','disabled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'disabled' COMMENT '状态:normal=启用,disabled=禁用',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '装修模板' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_decorate_page
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_decorate_page`;
CREATE TABLE `fa_shopro_decorate_page`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `decorate_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板',
  `type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型',
  `page` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据',
  `image` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '截图',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板数据' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_dispatch
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_dispatch`;
CREATE TABLE `fa_shopro_dispatch`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板名称',
  `type` enum('express','autosend') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'express' COMMENT '发货方式:express=快递物流,autosend=自动发货',
  `status` enum('normal','disabled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '状态:normal=正常,disabled=禁用',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发货模板' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_dispatch_autosend
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_dispatch_autosend`;
CREATE TABLE `fa_shopro_dispatch_autosend`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dispatch_id` int(11) NOT NULL COMMENT '配送模板',
  `type` enum('text','params') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'text' COMMENT '自动发货类型:text=固定内容,params=自定义内容',
  `content` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发货内容',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自动发货' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_dispatch_express
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_dispatch_express`;
CREATE TABLE `fa_shopro_dispatch_express`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dispatch_id` int(11) NOT NULL DEFAULT 0 COMMENT '配送模板',
  `type` enum('number','weight') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'number' COMMENT '计费方式:number=件数,weight=重量',
  `first_num` int(10) NOT NULL DEFAULT 0 COMMENT '首(重/件)数',
  `first_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '首(重/件)',
  `additional_num` int(10) NOT NULL DEFAULT 0 COMMENT '续(重/件)数',
  `additional_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '续(重/件)',
  `province_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省份',
  `city_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市',
  `district_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地区',
  `weigh` int(8) NOT NULL DEFAULT 0 COMMENT '权重',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `is_shang` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '是否送货上门:0=否,1=是',
  `free_shipping_num` int(11) NOT NULL DEFAULT 0 COMMENT '免运费数量阈值（0表示不设置）',
  `free_shipping_weight` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '免运费重量阈值（0表示不设置）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 68 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运费模板' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_feedback
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_feedback`;
CREATE TABLE `fa_shopro_feedback`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '反馈用户',
  `type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '反馈类型',
  `content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '反馈内容',
  `images` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '截图',
  `phone` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '处理状态',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统备注',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '意见反馈' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_goods
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_goods`;
CREATE TABLE `fa_shopro_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('normal','virtual','card') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '商品类型:normal=实体商品,virtual=虚拟商品,card=电子卡密',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `subtitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '副标题',
  `category_ids` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属分类',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品主图',
  `image_wh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主图宽高',
  `images` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '轮播图',
  `params` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数详情',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '图文详情',
  `original_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '原价',
  `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '价格',
  `is_sku` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否多规格',
  `limit_type` enum('none','daily','all') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'none' COMMENT '限购类型:none=不限购,daily=每日,all=累计',
  `limit_num` int(10) NOT NULL DEFAULT 0 COMMENT '限购数量',
  `likes` int(10) NOT NULL DEFAULT 0 COMMENT '收藏人数',
  `views` int(10) NOT NULL DEFAULT 0 COMMENT '浏览人数',
  `sales` int(10) NOT NULL DEFAULT 0 COMMENT '销量',
  `sales_show_type` enum('exact','sketchy') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'exact' COMMENT '销量显示类型:exact=精确的,sketchy=粗略的',
  `stock_show_type` enum('exact','sketchy') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'exact' COMMENT '库存显示类型:exact=精确的,sketchy=粗略的',
  `show_sales` int(10) NOT NULL DEFAULT 0 COMMENT '显示销量',
  `service_ids` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务标签',
  `dispatch_type` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发货方式',
  `dispatch_id` int(11) NOT NULL DEFAULT 0 COMMENT '发货模板',
  `is_offline` tinyint(3) NOT NULL DEFAULT 0 COMMENT '线下付款:0=否,1=是',
  `status` enum('up','hidden','down') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'up' COMMENT '商品状态:up=上架,hidden=隐藏,down=下架',
  `weigh` int(8) NOT NULL DEFAULT 0 COMMENT '权重',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  `min_buy` int(11) NOT NULL DEFAULT 1 COMMENT '最少购买数量',
  `send_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发货方式:0=物流快递,1=上门配送,2=自提',
  `shang_id` int(11) NULL DEFAULT NULL COMMENT '上门配送模板',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 54 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_goods_comment
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_goods_comment`;
CREATE TABLE `fa_shopro_goods_comment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `order_item_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单商品',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `user_type` enum('user','fake_user') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'user' COMMENT '用户类型:user=用户,fake_user=虚拟用户',
  `user_nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `user_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户头像',
  `level` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '评价星级',
  `content` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评价内容',
  `images` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评价图片',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '显示状态:normal=正常,hidden=隐藏',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '管理员',
  `reply_content` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回复内容',
  `reply_time` bigint(16) NULL DEFAULT NULL COMMENT '回复时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品评价' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_goods_service
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_goods_service`;
CREATE TABLE `fa_shopro_goods_service`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务标志',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品服务标签' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_goods_sku
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_goods_sku`;
CREATE TABLE `fa_shopro_goods_sku`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属规格',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `weigh` int(8) NOT NULL DEFAULT 0 COMMENT '权重',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 81 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品规格' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_goods_sku_price
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_goods_sku_price`;
CREATE TABLE `fa_shopro_goods_sku_price`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `goods_sku_ids` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格',
  `goods_sku_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格中文',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缩略图',
  `stock` int(10) NOT NULL DEFAULT 0 COMMENT '库存',
  `stock_warning` int(10) NULL DEFAULT NULL COMMENT '库存预警',
  `sales` int(10) NOT NULL DEFAULT 0 COMMENT '销量',
  `sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '货号',
  `weight` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '重量(KG)',
  `cost_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '成本价',
  `original_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '原价',
  `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '价格',
  `status` enum('up','down') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'up' COMMENT '商品状态:up=上架,down=下架',
  `weigh` int(8) NOT NULL DEFAULT 0 COMMENT '权重',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 114 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品规格价格' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_goods_stock_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_goods_stock_log`;
CREATE TABLE `fa_shopro_goods_stock_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人',
  `goods_sku_price_id` int(11) NOT NULL DEFAULT 0 COMMENT '规格',
  `goods_sku_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格名',
  `before` int(10) NOT NULL DEFAULT 0 COMMENT '补货前',
  `stock` int(10) NOT NULL DEFAULT 0 COMMENT '补货库存',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '补货备注',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品库存记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_goods_stock_warning
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_goods_stock_warning`;
CREATE TABLE `fa_shopro_goods_stock_warning`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `goods_sku_price_id` int(11) NOT NULL DEFAULT 0 COMMENT '规格',
  `goods_sku_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格名',
  `stock_warning` int(10) NOT NULL DEFAULT 0 COMMENT '预警值',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品库存预警' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_mplive_goods
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_mplive_goods`;
CREATE TABLE `fa_shopro_mplive_goods`  (
  `id` int(11) NOT NULL,
  `type` tinyint(4) NULL DEFAULT NULL COMMENT '商品来源',
  `audit_id` int(11) NULL DEFAULT NULL COMMENT '审核单ID',
  `goods_id` int(11) NULL DEFAULT NULL COMMENT '商城商品ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `cover_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面图',
  `price_type` tinyint(4) NULL DEFAULT NULL COMMENT '价格类型',
  `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '价格',
  `price2` decimal(10, 2) NULL DEFAULT NULL COMMENT '价格2',
  `third_party_tag` tinyint(4) NULL DEFAULT NULL COMMENT '添加商品标识',
  `third_party_appid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方小程序APPID',
  `on_shelves` tinyint(4) NULL DEFAULT NULL COMMENT '上架状态',
  `audit_status` tinyint(4) NULL DEFAULT NULL COMMENT '审核状态',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品链接',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '直播间商品' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_mplive_room
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_mplive_room`;
CREATE TABLE `fa_shopro_mplive_room`  (
  `roomid` int(10) NOT NULL COMMENT '房间号',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '直播间名称',
  `type` tinyint(4) NULL DEFAULT NULL COMMENT '直播方式',
  `status` int(10) NULL DEFAULT NULL COMMENT '状态',
  `is_feeds_public` tinyint(4) NULL DEFAULT NULL COMMENT '官方推荐',
  `goods` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品',
  `anchor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主播名称',
  `share_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分享图',
  `cover_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面图',
  `feeds_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '官方封面图',
  `close_replay` tinyint(4) NULL DEFAULT NULL COMMENT '关闭回放',
  `close_like` tinyint(4) NULL DEFAULT NULL COMMENT '关闭点赞',
  `close_kf` tinyint(4) NULL DEFAULT NULL COMMENT '关闭客服',
  `close_goods` tinyint(4) NULL DEFAULT NULL COMMENT '关闭商品橱窗',
  `close_comment` tinyint(4) NULL DEFAULT NULL COMMENT '关闭评论',
  `creater_openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建用户',
  `start_time` bigint(16) NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` bigint(16) NULL DEFAULT NULL COMMENT '结束时间',
  PRIMARY KEY (`roomid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '直播间' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_notification
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_notification`;
CREATE TABLE `fa_shopro_notification`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `notification_type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知类型',
  `type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '消息类型',
  `notifiable_id` int(11) NOT NULL DEFAULT 0 COMMENT '通知人',
  `notifiable_type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '通知人类型',
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `read_time` bigint(16) NULL DEFAULT NULL COMMENT '读取时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_notification_config
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_notification_config`;
CREATE TABLE `fa_shopro_notification_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息事件',
  `channel` enum('Sms','Email','Websocket','WechatOfficialAccount','WechatMiniProgram','WechatOfficialAccountBizsend') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发送渠道:Sms=短信,Email=邮件,Websocket=Websocket,WechatOfficialAccount=微信模板消息,WechatMiniProgram=小程序订阅消息,WechatOfficialAccountBizsend=公众号订阅消息',
  `type` enum('default','custom') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型:default=默认,custom=自定义',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置内容',
  `status` enum('enable','disabled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'enable' COMMENT '状态:enable=开启,disabled=关闭',
  `send_num` int(10) NOT NULL DEFAULT 0 COMMENT '发送次数',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息通知配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_order`;
CREATE TABLE `fa_shopro_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('goods','score') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'goods' COMMENT '订单类型:goods=商城订单,score=积分商城订单',
  `order_sn` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `activity_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动',
  `activity_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动类型',
  `promo_types` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '营销类型',
  `goods_original_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品原价',
  `goods_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品总价',
  `dispatch_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '运费',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户备注',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家备注',
  `status` enum('closed','cancel','unpaid','paid','completed','pending') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'unpaid' COMMENT '订单状态:closed=交易关闭,cancel=已取消,unpaid=未支付,paid=已支付,completed=已完成,pending=待定',
  `order_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
  `score_amount` int(10) NOT NULL DEFAULT 0 COMMENT '积分总数',
  `pay_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付总金额',
  `original_pay_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '原始支付总金额',
  `remain_pay_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '剩余支付金额',
  `paid_time` bigint(16) NULL DEFAULT NULL COMMENT '支付成功时间',
  `pay_mode` enum('online','offline') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'online' COMMENT '支付模式:online=线上支付,offline=线下支付',
  `apply_refund_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '申请退款状态:0=未申请,1=用户申请退款,-1=拒绝申请',
  `total_discount_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠总金额',
  `coupon_discount_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠券抵扣金额',
  `promo_discount_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '营销优惠金额',
  `coupon_id` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券',
  `invoice_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '发票开具状态:-1=不可开具,0=未申请,1=已申请',
  `ext` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附加信息',
  `platform` enum('H5','App','WechatOfficialAccount','WechatMiniProgram') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台:H5=H5,WechatOfficialAccount=微信公众号,WechatMiniProgram=微信小程序,App=App',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  `delivery_type` enum('express','store','pickup') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'express' COMMENT '配送方式:express=快递物流,store=上门配送,pickup=自提',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_sn`(`order_sn`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `createtime`(`createtime`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_order_action
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_order_action`;
CREATE TABLE `fa_shopro_order_action`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `order_item_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单商品',
  `oper_type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人类型',
  `oper_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人',
  `order_status` enum('closed','cancel','unpaid','paid','completed','pending') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'unpaid' COMMENT '订单状态:closed=交易关闭,cancel=已取消,unpaid=未支付,paid=已支付,completed=已完成,pending=待定',
  `dispatch_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '发货状态:-1=拒收,0=未发货,1=已发货,2=已收货',
  `aftersale_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '售后状态:-1=拒绝,0=未申请,1=申请售后,2=售后完成',
  `refund_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '退款状态:0=未退款,1=已同意,2=已完成',
  `comment_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '评价状态:0=未评价,1=已评价',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户备注',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `order_item_id`(`order_item_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单操作' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_order_address
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_order_address`;
CREATE TABLE `fa_shopro_order_address`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `consignee` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货手机',
  `province_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省份',
  `city_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市',
  `district_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地区',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省Id',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '市Id',
  `district_id` int(11) NOT NULL DEFAULT 0 COMMENT '区Id',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单收货信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_order_aftersale
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_order_aftersale`;
CREATE TABLE `fa_shopro_order_aftersale`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `aftersale_sn` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '售后单号',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型:refund=退款,return=退货,other=其他',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  `activity_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动',
  `activity_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动类型',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `order_item_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单商品',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `goods_sku_price_id` int(11) NOT NULL DEFAULT 0 COMMENT '规格',
  `goods_sku_text` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格名',
  `goods_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `goods_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品图片',
  `goods_original_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品原价',
  `goods_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品价格',
  `discount_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠费用',
  `goods_num` int(10) NOT NULL DEFAULT 0 COMMENT '购买数量',
  `dispatch_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '发货状态:-1=拒收,0=未发货,1=已发货,2=已收货',
  `dispatch_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '发货费用',
  `aftersale_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '售后状态:-2=已取消,-1=拒绝,0=未处理,1=申请售后,2=售后完成',
  `refund_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '退款状态:0=未退款,1=已同意',
  `refund_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请原因',
  `content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '相关描述',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `aftersale_sn`(`aftersale_sn`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单售后' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_order_aftersale_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_order_aftersale_log`;
CREATE TABLE `fa_shopro_order_aftersale_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `order_aftersale_id` int(11) NOT NULL DEFAULT 0 COMMENT '售后单',
  `oper_type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人类型',
  `oper_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人',
  `dispatch_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '发货状态:-1=拒收,0=未发货,1=已发货,2=已收货',
  `aftersale_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '售后状态:-1=拒绝,0=未申请,1=申请售后,2=售后完成',
  `refund_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '退款状态:0=未退款,1=已同意,2=已完成',
  `log_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志类型',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作内容',
  `images` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `order_aftersale_id`(`order_aftersale_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单售后记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_order_express
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_order_express`;
CREATE TABLE `fa_shopro_order_express`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `method` enum('input','api','upload') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'input' COMMENT '发货方式:input=手动发货,api=推送运单,upload=上传发货单',
  `driver` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '当前物流驱动',
  `express_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司',
  `express_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司编号',
  `express_no` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递单号',
  `status` enum('noinfo','collect','transport','delivery','signfor','refuse','difficulty','invalid','timeout','fail','back') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'noinfo' COMMENT '订单状态:noinfo=暂无信息,collect=已揽件,transport=运输中,delivery=派送中,signfor=已签收,refuse=用户拒收,difficulty=问题件,invalid=无效件,timeout=超时单,fail=签收失败,back=退回',
  `ext` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附加信息',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '快递包裹' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_order_express_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_order_express_log`;
CREATE TABLE `fa_shopro_order_express_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `order_express_id` int(11) NOT NULL DEFAULT 0 COMMENT '快递包裹',
  `content` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
  `change_date` datetime NULL DEFAULT NULL COMMENT '变动时间',
  `status` enum('noinfo','collect','transport','delivery','signfor','refuse','difficulty','invalid','timeout','fail','back') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'noinfo' COMMENT '订单状态:noinfo=暂无信息,collect=已揽件,transport=运输中,delivery=派送中,signfor=已签收,refuse=用户拒收,difficulty=问题件,invalid=无效件,timeout=超时单,fail=签收失败,back=退回',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `order_express_id`(`order_express_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物流信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_order_invoice
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_order_invoice`;
CREATE TABLE `fa_shopro_order_invoice`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('person','company') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发票类型:person=个人,company=企事业单位',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `tax_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '税号',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位地址',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号码',
  `bank_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户银行',
  `bank_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行账户',
  `amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '金额',
  `download_urls` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发票地址',
  `invoice_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '开票金额',
  `status` enum('cancel','unpaid','waiting','finish') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'unpaid' COMMENT '状态:cancel=已取消,unpaid=未支付,waiting=等待处理,finish=已开具',
  `finish_time` bigint(16) NOT NULL DEFAULT 0 COMMENT '开具时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发票' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_order_item
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_order_item`;
CREATE TABLE `fa_shopro_order_item`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `goods_type` enum('normal','virtual','card') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '商品类型:normal=实体商品,virtual=虚拟商品,card=电子卡密',
  `goods_sku_price_id` int(11) NOT NULL DEFAULT 0 COMMENT '规格',
  `activity_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动',
  `activity_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动类型',
  `promo_types` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '营销类型',
  `item_goods_sku_price_id` int(11) NOT NULL DEFAULT 0 COMMENT '活动规格|积分商城规格',
  `goods_sku_text` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格名',
  `goods_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `goods_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片',
  `goods_original_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品原价',
  `goods_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品价格',
  `goods_num` int(10) NOT NULL DEFAULT 0 COMMENT '购买数量',
  `goods_weight` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '商品重量(KG)',
  `discount_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠费用',
  `pay_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付金额(不含运费)',
  `dispatch_status` tinyint(3) NOT NULL DEFAULT 0 COMMENT '发货状态:-1=拒收,0=未发货,1=已发货,2=已收货',
  `dispatch_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '发货费用',
  `dispatch_type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发货方式',
  `dispatch_id` int(11) NOT NULL DEFAULT 0 COMMENT '发货模板',
  `aftersale_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '售后状态:-1=拒绝,0=未申请,1=申请售后,2=售后完成',
  `comment_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '评价状态:0=未评价,1=已评价',
  `refund_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '退款状态:0=未退款,1=已同意,2=已完成',
  `refund_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `refund_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款原因',
  `order_express_id` int(11) NOT NULL DEFAULT 0 COMMENT '快递包裹',
  `ext` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附加信息',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `goods_id`(`goods_id`) USING BTREE,
  INDEX `activity_id`(`activity_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单商品' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_order_pickup
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_order_pickup`;
CREATE TABLE `fa_shopro_order_pickup`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单ID',
  `pickup_id` int(11) NOT NULL DEFAULT 0 COMMENT '自提点ID',
  `pickup_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自提点名称',
  `pickup_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自提点地址',
  `pickup_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自提点电话',
  `business_hours` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '营业时间',
  `pickup_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取货码',
  `pickup_time` bigint(16) NULL DEFAULT NULL COMMENT '预计取货时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `pickup_id`(`pickup_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单自提信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_pay
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_pay`;
CREATE TABLE `fa_shopro_pay`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单类型',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `pay_sn` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付单号',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `pay_type` enum('wechat','alipay','money','score','offline') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付方式:wechat=微信支付,alipay=支付宝,money=钱包支付,score=积分支付,offline=线下支付',
  `pay_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `real_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实际金额',
  `transaction_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易单号',
  `buyer_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易用户',
  `payment_json` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易原始数据',
  `paid_time` bigint(16) NULL DEFAULT NULL COMMENT '交易时间',
  `status` enum('unpaid','paid','refund') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付状态:unpaid=未支付,paid=已支付,refund=已退款',
  `refund_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '已退款金额',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `pay_sn`(`pay_sn`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_pay_config
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_pay_config`;
CREATE TABLE `fa_shopro_pay_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `type` enum('wechat','alipay') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型:wechat=微信,alipay=支付宝',
  `params` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数',
  `status` enum('normal','disabled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '状态:normal=正常,disabled=禁用',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_refund
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_refund`;
CREATE TABLE `fa_shopro_refund`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `refund_sn` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款单号',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单',
  `pay_id` int(11) NOT NULL DEFAULT 0 COMMENT '支付',
  `pay_type` enum('wechat','alipay','money','score','offline') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付方式:wechat=微信支付,alipay=支付宝,money=钱包支付,score=积分支付,offline=线下支付',
  `refund_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `refund_type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款类型',
  `refund_method` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款方式',
  `status` enum('ing','completed','fail') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'ing' COMMENT '退款状态:ing=退款中,completed=退款完成,fail=退款失败',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `platform` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下单平台',
  `payment_json` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易原始数据',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `refund_sn`(`refund_sn`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退款' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_score_sku_price
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_score_sku_price`;
CREATE TABLE `fa_shopro_score_sku_price`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `goods_sku_price_id` int(11) NOT NULL DEFAULT 0 COMMENT '规格',
  `stock` int(10) NOT NULL DEFAULT 0 COMMENT '库存',
  `sales` int(10) NOT NULL DEFAULT 0 COMMENT '销量',
  `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '价格',
  `score` int(10) NOT NULL DEFAULT 0 COMMENT '积分',
  `status` enum('up','down') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'up' COMMENT '商品状态:up=上架,down=下架',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `goods_id`(`goods_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '积分商城' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_search_history
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_search_history`;
CREATE TABLE `fa_shopro_search_history`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `keyword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关键词',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '搜索历史' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_share
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_share`;
CREATE TABLE `fa_shopro_share`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `spm` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原始spm',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `share_id` int(11) NOT NULL DEFAULT 0 COMMENT '分享人',
  `page` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分享页面',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分享页面参数',
  `platform` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分享平台',
  `from` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分享方式',
  `ext` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '附加信息',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `share_id`(`share_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户分享记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_third_oauth
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_third_oauth`;
CREATE TABLE `fa_shopro_third_oauth`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
  `provider` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '厂商',
  `platform` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台',
  `openid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台唯一标识',
  `unionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主体唯一标识',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `login_num` int(10) NOT NULL DEFAULT 0 COMMENT '使用登录次数',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`, `openid`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '第三方用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_trade_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_trade_order`;
CREATE TABLE `fa_shopro_trade_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('recharge') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单类型:recharge=余额充值',
  `order_sn` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `status` enum('closed','cancel','unpaid','paid','completed') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'unpaid' COMMENT '订单状态:closed=交易关闭,cancel=已取消,unpaid=未支付,paid=已支付,completed=已完成',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户备注',
  `order_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
  `pay_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付总金额',
  `remain_pay_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '剩余支付金额',
  `paid_time` bigint(16) NULL DEFAULT NULL COMMENT '支付成功时间',
  `ext` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附加信息',
  `platform` enum('H5','App','WechatOfficialAccount','WechatMiniProgram') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台:H5=H5,WechatOfficialAccount=微信公众号,WechatMiniProgram=微信小程序,App=App',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_sn`(`order_sn`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `createtime`(`createtime`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '交易订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_user_account
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_user_account`;
CREATE TABLE `fa_shopro_user_account`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账户类型:wechat=微信,alipay=支付宝,bank=银行账户',
  `account_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `account_header` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账户名',
  `account_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账号',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '提现账户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_user_address
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_user_address`;
CREATE TABLE `fa_shopro_user_address`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `is_default` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '默认:0=否,1=是',
  `consignee` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货手机',
  `province_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省份',
  `city_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市',
  `district_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地区',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省Id',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '市Id',
  `district_id` int(11) NOT NULL DEFAULT 0 COMMENT '区Id',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户收货地址' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_user_coupon
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_user_coupon`;
CREATE TABLE `fa_shopro_user_coupon`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `coupon_id` int(11) NOT NULL DEFAULT 0 COMMENT '优惠券',
  `use_order_id` int(11) NOT NULL DEFAULT 0 COMMENT '使用订单',
  `use_time` bigint(16) NULL DEFAULT NULL COMMENT '使用时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `coupon_id`(`coupon_id`) USING BTREE,
  INDEX `use_time`(`use_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户优惠券' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_user_goods_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_user_goods_log`;
CREATE TABLE `fa_shopro_user_goods_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `type` enum('favorite','views') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型:favorite=收藏,views=浏览记录',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `goods_id`(`goods_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户商品收藏' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_user_invoice
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_user_invoice`;
CREATE TABLE `fa_shopro_user_invoice`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `type` enum('person','company') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发票类型:person=个人,company=单位',
  `name` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `tax_no` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '税号',
  `address` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位地址',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号码',
  `bank_name` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户银行',
  `bank_no` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行账户',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '我的发票' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_user_wallet_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_user_wallet_log`;
CREATE TABLE `fa_shopro_user_wallet_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户',
  `type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型:money=余额,commission=佣金,score=积分',
  `event` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '事件:money_recharge=充值,money_consume=余额消费,commission_withdraw=提现,commission_transfer=佣金转余额,commission_reward=佣金奖励,score_consume=积分消费,score_sign=积分签到,activity=活动赠送',
  `amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '数量',
  `before` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变动前',
  `after` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变动后',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `ext` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展信息',
  `oper_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人类型',
  `oper_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户资金日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_wechat_material
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_wechat_material`;
CREATE TABLE `fa_shopro_wechat_material`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('text','link') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型:text=文字,link=链接',
  `content` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '素材管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_wechat_menu
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_wechat_menu`;
CREATE TABLE `fa_shopro_wechat_menu`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单名称',
  `rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '菜单规则',
  `status` int(1) NOT NULL DEFAULT 0 COMMENT '状态:0=未发布,1=已发布',
  `publishtime` bigint(16) NULL DEFAULT NULL COMMENT '发布时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信公众号菜单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_wechat_reply
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_wechat_reply`;
CREATE TABLE `fa_shopro_wechat_reply`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group` enum('keywords','subscribe','default') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型:keywords=关键字回复,subscribe=关注回复,default=默认回复',
  `type` enum('text','link','video','voice','image','news') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型:text=文本,link=链接,video=视频,audio=音频,image=图像,media=图文消息',
  `status` enum('enable','disabled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态:enable=启用,disabled=禁用',
  `keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关键字',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回复内容',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自动回复' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_withdraw
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_withdraw`;
CREATE TABLE `fa_shopro_withdraw`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '提现用户',
  `amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现金额',
  `paid_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实际到账',
  `charge_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '手续费',
  `charge_rate` decimal(10, 3) NOT NULL DEFAULT 0.000 COMMENT '手续费率',
  `withdraw_sn` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '提现单号',
  `withdraw_type` enum('bank','wechat','alipay') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '提现类型:bank=银行卡,wechat=微信零钱,alipay=支付宝账户',
  `withdraw_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提现信息',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '提现状态:-1=已拒绝,0=待审核,1=处理中,2=已处理',
  `platform` enum('H5','App','WechatOfficialAccount','WechatMiniProgram') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台:H5=H5,WechatOfficialAccount=微信公众号,WechatMiniProgram=微信小程序,App=App',
  `payment_json` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易原始数据',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `withdraw_sn`(`withdraw_sn`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户提现' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shopro_withdraw_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_shopro_withdraw_log`;
CREATE TABLE `fa_shopro_withdraw_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `withdraw_id` int(11) NOT NULL DEFAULT 0 COMMENT '提现ID',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志内容',
  `oper_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人类型',
  `oper_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '提现日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_sms
-- ----------------------------
DROP TABLE IF EXISTS `fa_sms`;
CREATE TABLE `fa_sms`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `event` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '事件',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号',
  `code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证码',
  `times` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '验证次数',
  `ip` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'IP',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短信验证码表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_test
-- ----------------------------
DROP TABLE IF EXISTS `fa_test`;
CREATE TABLE `fa_test`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NULL DEFAULT 0 COMMENT '会员ID',
  `admin_id` int(10) NULL DEFAULT 0 COMMENT '管理员ID',
  `category_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '分类ID(单选)',
  `category_ids` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类ID(多选)',
  `tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '标签',
  `week` enum('monday','tuesday','wednesday') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '星期(单选):monday=星期一,tuesday=星期二,wednesday=星期三',
  `flag` set('hot','index','recommend') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '标志(多选):hot=热门,index=首页,recommend=推荐',
  `genderdata` enum('male','female') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'male' COMMENT '性别(单选):male=男,female=女',
  `hobbydata` set('music','reading','swimming') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '爱好(多选):music=音乐,reading=读书,swimming=游泳',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图片',
  `images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图片组',
  `attachfile` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '附件',
  `keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '关键字',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '描述',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '省市',
  `array` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '数组:value=值',
  `json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '配置:key=名称,value=值',
  `multiplejson` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '二维数组:title=标题,intro=介绍,author=作者,age=年龄',
  `price` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '价格',
  `views` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '点击',
  `workrange` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '时间区间',
  `startdate` date NULL DEFAULT NULL COMMENT '开始日期',
  `activitytime` datetime NULL DEFAULT NULL COMMENT '活动时间(datetime)',
  `year` year NULL DEFAULT NULL COMMENT '年',
  `times` time NULL DEFAULT NULL COMMENT '时间',
  `refreshtime` bigint(16) NULL DEFAULT NULL COMMENT '刷新时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  `weigh` int(10) NULL DEFAULT 0 COMMENT '权重',
  `switch` tinyint(1) NULL DEFAULT 0 COMMENT '开关',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'normal' COMMENT '状态',
  `state` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '状态值:0=禁用,1=正常,2=推荐',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '测试表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_user
-- ----------------------------
DROP TABLE IF EXISTS `fa_user`;
CREATE TABLE `fa_user`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '组别ID',
  `username` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户名',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '昵称',
  `password` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码',
  `salt` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码盐',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '电子邮箱',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '等级',
  `gender` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '性别',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `bio` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '格言',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '余额',
  `commission` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '佣金',
  `score` int(10) NOT NULL DEFAULT 0 COMMENT '积分',
  `successions` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '连续登录天数',
  `maxsuccessions` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '最大连续登录天数',
  `prevtime` bigint(16) NULL DEFAULT NULL COMMENT '上次登录时间',
  `logintime` bigint(16) NULL DEFAULT NULL COMMENT '登录时间',
  `loginip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录IP',
  `loginfailure` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '失败次数',
  `loginfailuretime` bigint(16) NULL DEFAULT NULL COMMENT '最后登录失败时间',
  `joinip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '加入IP',
  `jointime` bigint(16) NULL DEFAULT NULL COMMENT '加入时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `token` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'Token',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '状态',
  `verification` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证',
  `parent_user_id` int(11) NULL DEFAULT NULL COMMENT '上级用户',
  `total_consume` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '累计消费',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `username`(`username`) USING BTREE,
  INDEX `email`(`email`) USING BTREE,
  INDEX `mobile`(`mobile`) USING BTREE,
  INDEX `parent_user_id`(`parent_user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_user_group
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_group`;
CREATE TABLE `fa_user_group`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '组名',
  `rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '权限节点',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '添加时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员组表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_user_money_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_money_log`;
CREATE TABLE `fa_user_money_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变更余额',
  `before` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变更前余额',
  `after` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变更后余额',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员余额变动表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_user_rule
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_rule`;
CREATE TABLE `fa_user_rule`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int(10) NULL DEFAULT NULL COMMENT '父ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '标题',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `ismenu` tinyint(1) NULL DEFAULT NULL COMMENT '是否菜单',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NULL DEFAULT 0 COMMENT '权重',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_user_score_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_score_log`;
CREATE TABLE `fa_user_score_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `score` int(10) NOT NULL DEFAULT 0 COMMENT '变更积分',
  `before` int(10) NOT NULL DEFAULT 0 COMMENT '变更前积分',
  `after` int(10) NOT NULL DEFAULT 0 COMMENT '变更后积分',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员积分变动表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_user_token
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_token`;
CREATE TABLE `fa_user_token`  (
  `token` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Token',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `expiretime` bigint(16) NULL DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`token`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员Token表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_version
-- ----------------------------
DROP TABLE IF EXISTS `fa_version`;
CREATE TABLE `fa_version`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `oldversion` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '旧版本号',
  `newversion` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '新版本号',
  `packagesize` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '包大小',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '升级内容',
  `downloadurl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '下载地址',
  `enforce` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '强制更新',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '版本表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
