define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'lotteryrecord/index' + location.search,
                    add_url: 'lotteryrecord/add',
                    edit_url: 'lotteryrecord/edit',
                    del_url: 'lotteryrecord/del',
                    multi_url: 'lotteryrecord/multi',
                    import_url: 'lotteryrecord/import',
                    table: 'lotteryrecord',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'user_id', title: __('User_id')},
                        {field: 'prize_id', title: __('Prize_id')},
                        {field: 'prize_name', title: __('Prize_name'), operate: 'LIKE'},
                        {field: 'prize_image', title: __('Prize_image'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'score_value', title: __('Score_value')},
                        {field: 'shipping_fee', title: __('Shipping_fee'), operate:'BETWEEN'},
                        {field: 'choice_type', title: __('Choice_type'), searchList: {"goods":__('Choice_type goods'),"score":__('Choice_type score')}, formatter: Table.api.formatter.normal},
                        {field: 'delivery_status', title: __('Delivery_status'), searchList: {"pending":__('Delivery_status pending'),"processing":__('Delivery_status processing'),"shipped":__('Delivery_status shipped'),"completed":__('Delivery_status completed')}, formatter: Table.api.formatter.status},
                        {field: 'delivery_time', title: __('Delivery_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'delivery_company', title: __('Delivery_company'), operate: 'LIKE'},
                        {field: 'delivery_number', title: __('Delivery_number'), operate: 'LIKE'},
                        {field: 'delivery_remark', title: __('Delivery_remark'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'receiver_name', title: __('Receiver_name'), operate: 'LIKE'},
                        {field: 'receiver_phone', title: __('Receiver_phone'), operate: 'LIKE'},
                        {field: 'receiver_address', title: __('Receiver_address'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'user.nickname', title: __('User.nickname'), operate: 'LIKE'},
                        {field: 'lotteryprize.name', title: __('Lotteryprize.name'), operate: 'LIKE'},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
