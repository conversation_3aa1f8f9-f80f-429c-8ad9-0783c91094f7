define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: () => {
            const { reactive, onMounted } = Vue
            const index = {
                setup() {
                    const state = reactive({
                        dispatch_type: 'express',
                        data: [],
                    })

                    function getData() {
                        let params = {
                            page: pagination.page,
                            list_rows: pagination.list_rows,
                            type: state.dispatch_type
                        };
                        
                        // 根据不同的tab传递不同的is_shang参数
                        if (state.dispatch_type === 'express_home') {
                            params.type = 'express';
                            params.is_shang = 1;
                        } else if (state.dispatch_type === 'express') {
                            params.type = 'express';
                            params.is_shang = 0;
                        }
                        
                        Fast.api.ajax({
                            url: 'shopro/dispatch/dispatch',
                            type: 'GET',
                            data: params,
                        }, function (ret, res) {
                            state.data = res.data.data
                            pagination.total = res.data.total
                            return false
                        }, function (ret, res) { })
                    }

                    const pagination = reactive({
                        page: 1,
                        list_rows: 10,
                        total: 0,
                    })

                    function onChangeTab() {
                        pagination.page = 1
                        getData()
                    }

                    function onAdd() {
                        let dispatchType = state.dispatch_type;
                        let isHomeDelivery = false;
                        if (dispatchType === 'express_home') {
                            dispatchType = 'express';
                            isHomeDelivery = true;
                        } else if (dispatchType === 'express') {
                            dispatchType = 'express';
                            isHomeDelivery = false;
                        }
                        Fast.api.open(`shopro/dispatch/dispatch/add?type=add&dispatch_type=${dispatchType}&is_home_delivery=${isHomeDelivery}`, "添加", {
                            callback() {
                                getData()
                            }
                        })
                    }
                    function onEdit(id) {
                        let dispatchType = state.dispatch_type;
                        let isHomeDelivery = false;
                        if (dispatchType === 'express_home') {
                            dispatchType = 'express';
                            isHomeDelivery = true;
                        } else if (dispatchType === 'express') {
                            dispatchType = 'express';
                            isHomeDelivery = false;
                        }
                        Fast.api.open(`shopro/dispatch/dispatch/edit?type=edit&id=${id}&dispatch_type=${dispatchType}&is_home_delivery=${isHomeDelivery}`, "编辑", {
                            callback() {
                                getData()
                            }
                        })
                    }
                    function onCopy(id) {
                        let dispatchType = state.dispatch_type;
                        let isHomeDelivery = false;
                        if (dispatchType === 'express_home') {
                            dispatchType = 'express';
                            isHomeDelivery = true;
                        } else if (dispatchType === 'express') {
                            dispatchType = 'express';
                            isHomeDelivery = false;
                        }
                        Fast.api.open(`shopro/dispatch/dispatch/add?type=copy&id=${id}&dispatch_type=${dispatchType}&is_home_delivery=${isHomeDelivery}`, "复制", {
                            callback() {
                                getData()
                            }
                        })
                    }
                    function onDelete(id) {
                        let dispatchType = state.dispatch_type;
                        if (dispatchType === 'express_home') {
                            dispatchType = 'express';
                        }
                        Fast.api.ajax({
                            url: `shopro/dispatch/dispatch/delete/id/${id}?type=${dispatchType}`,
                            type: 'DELETE',
                        }, function (ret, res) {
                            getData()
                        }, function (ret, res) { })
                    }

                    onMounted(() => {
                        getData()
                    })

                    return {
                        state,
                        getData,
                        pagination,
                        onChangeTab,
                        onAdd,
                        onEdit,
                        onCopy,
                        onDelete
                    }
                }
            }
            createApp('index', index);
        },
        add: () => {
            Controller.form();
        },
        edit: () => {
            Controller.form();
        },
        form: () => {
            const { reactive, onMounted, getCurrentInstance } = Vue
            const addEdit = {
                setup() {
                    const { proxy } = getCurrentInstance();

                    const state = reactive({
                        type: new URLSearchParams(location.search).get('type'),
                        id: new URLSearchParams(location.search).get('id'),
                        dispatch_type: new URLSearchParams(location.search).get('dispatch_type'),
                        priceType: 'number',
                        deliveryType: '0',
                        isHomeDelivery: new URLSearchParams(location.search).get('is_home_delivery') === 'true',
                    })

                    const form = reactive({
                        model: {
                            // dispatch_type: state.dispatch_type,
                            name: '',
                            type: state.dispatch_type,
                            express: [],
                            autosend: {
                                type: "text",
                                content: ""
                            }
                        },
                        rules: {
                            name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
                            express: {
                                first_num: [{ required: true, message: '请输入数量', trigger: 'blur' }],
                                first_price: [{ required: true, message: '请输入运费', trigger: 'blur' }],
                                additional_num: [{ required: true, message: '请输入数量', trigger: 'blur' }],
                                additional_price: [{ required: true, message: '请输入续费', trigger: 'blur' }],
                                district_text: [{ required: true, message: '请选择可配送区域', trigger: 'blur' }],
                                free_shipping_num: [{ required: false, message: '请输入免运费数量', trigger: 'blur' }],
                                free_shipping_weight: [{ required: false, message: '请输入免运费重量', trigger: 'blur' }],
                            }
                        },
                    })

                    function getDetail() {
                        Fast.api.ajax({
                            url: `shopro/dispatch/dispatch/detail/id/${state.id}`,
                            type: 'GET',
                            data: {
                                type: state.dispatch_type,
                            }
                        }, function (ret, res) {
                            form.model = res.data;
                            if (state.dispatch_type == 'express') {
                                state.priceType = form.model.express.length > 0 ? form.model.express[0].type : 'number';
                            // 设置配送方式默认值
                            if (form.model.express.length > 0) {
                                state.deliveryType = form.model.express[0].is_shang || '0';
                            }
                            // 如果是上门配送模式，强制设置为上门配送
                            if (state.isHomeDelivery) {
                                state.deliveryType = '1';
                            }
                            }
                            return false
                        }, function (ret, res) { })
                    }

                    function onAddTemplate() {
                        form.model.express.push({
                            type: state.priceType,
                            first_num: 0,
                            first_price: 0,
                            additional_num: 0,
                            additional_price: 0,
                            province_ids: '',
                            city_ids: '',
                            district_ids: '',
                            is_shang: state.isHomeDelivery ? '1' : '0',
                            free_shipping_num: 0,
                            free_shipping_weight: 0,
                        });
                    }
                    function onDeleteTemplate(index) {
                        form.model.express.splice(index, 1);
                    }

                    function onSelectArea(index) {
                        let selected = {
                            province: form.model.express[index].province_ids,
                            city: form.model.express[index].city_ids,
                            district: form.model.express[index].district_ids,
                        }
                        Fast.api.open(`shopro/data/area/select?selected=${encodeURI(JSON.stringify(selected))}`, "选择地区", {
                            callback(data) {
                                let text = [];
                                for (var key in data) {
                                    let ids = [];
                                    for (var id in data[key]) {
                                        ids.push(id);
                                        text.push(data[key][id]);
                                    }
                                    form.model.express[index][key + '_ids'] = ids.join(',');
                                }
                                form.model.express[index].district_text = text.join(',');
                            }
                        })
                    }

                    function onChangeAutosendType(type) {
                        form.model.autosend.content = type == 'text' ? '' : []
                    }
                    function onAddContent() {
                        console.log(123, form.model.autosend.content)
                        if(!form.model.autosend.content){
                            form.model.autosend.content=[]
                        }
                        form.model.autosend.content.push({
                            title: '',
                            content: '',
                        });
                    }
                    function onDeleteContent(index) {
                        form.model.autosend.content.splice(index, 1);
                    }

                    function onConfirm() {
                        proxy.$refs['formRef'].validate((valid) => {
                            if (valid) {
                                let submitForm = JSON.parse(JSON.stringify(form.model));

                                if (state.dispatch_type == 'express') {
                                    submitForm.express.forEach((item) => {
                                        item.type = state.priceType;
                                        item.is_shang = state.isHomeDelivery ? '1' : '0';
                                    });

                                    if (state.type == 'copy') {
                                        delete submitForm.id;
                                        submitForm.express.forEach((item) => {
                                            delete item.id;
                                        });
                                    }
                                }else if(state.dispatch_type == 'autosend'){
                                    if (state.type == 'copy') {
                                      delete submitForm.id;
                                    }
                                  }
                                Fast.api.ajax({
                                    url: state.type == 'add' || state.type == 'copy' ? 'shopro/dispatch/dispatch/add' : `shopro/dispatch/dispatch/edit/id/${state.id}`,
                                    type: 'POST',
                                    data: submitForm,
                                }, function (ret, res) {
                                    Fast.api.close()
                                }, function (ret, res) { })
                            }
                        });
                    }

                    onMounted(() => {
                        (state.type == 'edit' || state.type == 'copy') && getDetail()
                    })

                    return {
                        state,
                        form,
                        onAddTemplate,
                        onDeleteTemplate,
                        onSelectArea,
                        onChangeAutosendType,
                        onAddContent,
                        onDeleteContent,
                        onConfirm,
                        state
                    }
                }
            }
            createApp('addEdit', addEdit);
        }
    };
    return Controller;
});
